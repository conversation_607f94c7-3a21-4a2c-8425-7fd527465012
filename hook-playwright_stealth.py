#!/usr/bin/env python3
"""
playwright_stealth 专用打包钩子
确保playwright_stealth包的所有模块被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有playwright_stealth子模块
hiddenimports = collect_submodules('playwright_stealth')

# 添加可能缺失的模块
hiddenimports.extend([
    'playwright_stealth.stealth',
    'playwright_stealth.core',
    'playwright_stealth.core._stealth_config',
    'playwright_stealth.properties',
    'playwright_stealth.properties._properties',
])

# 收集playwright_stealth的数据文件
datas = collect_data_files('playwright_stealth')

print(f"playwright_stealth打包钩子: 包含了 {len(hiddenimports)} 个模块, {len(datas)} 个数据文件") 