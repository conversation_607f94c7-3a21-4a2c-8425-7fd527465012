@echo off
chcp 65001 >nul
echo 🤖 启动持久化Google AI Studio自动化工具
echo ============================================================

REM 检查配置文件
if not exist "config.json" (
    echo ❌ 配置文件不存在
    echo 💡 请先编辑 config.json 文件，填入您的Google账号信息
    echo.
    echo 📝 配置文件模板:
    echo {
    echo   "google_account": {
    echo     "email": "<EMAIL>",
    echo     "password": "your_password_here"
    echo   }
    echo }
    echo.
    pause
    exit /b 1
)

REM 检查Python环境
echo 🔍 检查Python环境...
C:\Users\<USER>\miniconda3\Scripts\activate.bat google_ai_studio >nul 2>&1
if errorlevel 1 (
    echo ❌ Python环境 google_ai_studio 不存在
    echo 💡 请先创建并激活Python环境
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 启动程序
echo 🚀 启动程序...
echo ============================================================
powershell -Command "& { C:\Users\<USER>\miniconda3\Scripts\activate.bat google_ai_studio; python persistent_ai_studio.py }"

echo.
echo 👋 程序已退出
pause
