# 🚀 Crawl4AI Web Service

基于 Crawl4AI 的网页内容抓取 Web 服务，提供 RESTful API 接口，可将任意网页内容转换为 Markdown 格式，**自动移除所有超链接**。

## ✨ 功能特性

- 🌐 支持任意网页内容抓取
- 📝 自动转换为 Markdown 格式  
- 🔗 **自动移除所有超链接** (新功能!)
- ⚡ 异步处理，高性能
- 🔧 可配置超时时间
- 📊 返回详细的处理时间统计
- 🏥 内置健康检查接口
- 📚 自动生成 API 文档

## 🔗 超链接移除功能

服务会自动移除以下类型的超链接：

- ✅ 普通链接：`[文本](url)` → `文本`
- ✅ 图片链接：`![描述](url)` → `描述`
- ✅ 引用链接：`[文本][ref]` + `[ref]: url` → `文本`
- ✅ 直接链接：`<https://example.com>` → `(移除)`
- ✅ 裸露URL：`https://example.com` → `(移除)`

这使得返回的 Markdown 内容更加纯净，专注于文本内容，非常适合 AI 处理和分析。

## 🛠️ 安装依赖

确保您已经安装了必要的依赖：

```bash
# 安装基础依赖
pip install fastapi uvicorn pydantic requests

# 确保 crawl4ai 已正确安装和配置
# 如果未安装，请按照项目主目录的说明进行安装
```

## 🚀 启动服务

```bash
# 启动服务
python web_service.py
```

服务启动后，您会看到类似以下的输出：

```
🚀 启动 Crawl4AI Web Service...
📡 服务将在 http://localhost:5000 运行
📚 API文档可在 http://localhost:5000/docs 查看
🔍 /markdown 接口用于网页内容抓取
🔗 自动移除markdown内容中的超链接
INFO:     Uvicorn running on http://0.0.0.0:5000 (Press CTRL+C to quit)
```

## 📋 API 接口

### 1. 根路径 - 服务信息

**请求:**
```
GET http://localhost:5000/
```

**响应:**
```json
{
    "message": "Crawl4AI Web Service",
    "version": "1.0.0",
    "description": "网页内容抓取服务，自动移除markdown中的超链接",
    "endpoints": {
        "/markdown": "POST - 网页内容抓取并转换为Markdown格式（无超链接）"
    }
}
```

### 2. 健康检查

**请求:**
```
GET http://localhost:5000/health
```

**响应:**
```json
{
    "status": "healthy",
    "service": "crawl4ai-web-service"
}
```

### 3. Markdown 内容抓取 (主要功能)

**请求:**
```
POST http://localhost:5000/markdown
Content-Type: application/json

{
    "url": "https://asia.unc.edu/about/people",
    "timeout": 60
}
```

**响应:**
```json
{
    "success": true,
    "url": "https://asia.unc.edu/about/people",
    "markdown_content": "# 网页标题\n\n网页内容的 Markdown 格式（已移除所有超链接）...",
    "processing_time": 3.005
}
```

## 🧪 测试服务

我们提供了完整的测试脚本：

```bash
# 运行基本测试
python test_service.py

# 单独测试链接移除功能
python test_link_removal.py
```

测试脚本会自动：
1. 测试根路径接口
2. 测试健康检查接口  
3. 测试核心的 /markdown 接口功能
4. **验证超链接移除功能**

## 📚 API 文档

服务启动后，您可以通过以下地址查看交互式 API 文档：

- **Swagger UI**: http://localhost:5000/docs
- **ReDoc**: http://localhost:5000/redoc

## 💡 使用示例

### Python 客户端示例

```python
import requests

# 服务地址
base_url = "http://localhost:5000"

# 请求数据
data = {
    "url": "https://example.com",
    "timeout": 30
}

# 发送请求
response = requests.post(f"{base_url}/markdown", json=data)

if response.status_code == 200:
    result = response.json()
    
    print(f"抓取成功: {result['success']}")
    print(f"处理时间: {result['processing_time']}秒")
    print(f"内容长度: {len(result['markdown_content'])} 字符")
    
    # 保存到文件 - 内容已自动移除超链接
    with open("output.md", "w", encoding="utf-8") as f:
        f.write(result['markdown_content'])
        
else:
    print(f"请求失败: {response.status_code}")
```

### cURL 示例

```bash
curl -X POST "http://localhost:5000/markdown" \
     -H "Content-Type: application/json" \
     -d '{
       "url": "https://asia.unc.edu/about/people",
       "timeout": 60
     }'
```

### JavaScript 示例

```javascript
const response = await fetch('http://localhost:5000/markdown', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        url: 'https://example.com',
        timeout: 60
    })
});

const result = await response.json();

if (result.success) {
    console.log('抓取成功!');
    console.log('Markdown内容（无超链接）:', result.markdown_content);
} else {
    console.log('抓取失败:', result.markdown_content);
}
```

## ⚙️ 配置选项

### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `url` | string | ✓ | - | 要抓取的网页URL |
| `timeout` | integer | ✗ | 60 | 超时时间（秒） |

### 响应字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `success` | boolean | 抓取是否成功 |
| `url` | string | 请求的URL |
| `markdown_content` | string | 转换后的Markdown内容（**已移除超链接**） |
| `processing_time` | float | 处理耗时（秒） |

## 🔧 服务配置

可以通过修改 `web_service.py` 中的以下参数来调整服务配置：

```python
# 端口配置
port = 5000

# 主机配置
host = "0.0.0.0"  # 允许外部访问

# 日志级别
log_level = "info"
```

## 🔗 超链接移除逻辑

链接移除功能使用精心设计的正则表达式，按以下顺序处理：

1. **图片链接** `![alt](url)` → `alt`
2. **普通链接** `[text](url)` → `text`
3. **引用链接** `[text][ref]` → `text`
4. **引用定义** `[ref]: url` → (移除整行)
5. **直接链接** `<url>` → (移除)
6. **裸露URL** `https://...` → (移除)
7. **清理多余空行**

这确保了所有类型的链接都被正确处理，同时保留有用的文本内容。

## 🐛 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口 5000 是否被其他程序占用
   - 确保所有依赖已正确安装

2. **爬取失败**
   - 检查目标网站是否可访问
   - 尝试增加 timeout 值
   - 检查网络连接

3. **内容为空**
   - 某些网站可能使用反爬虫机制
   - 可以尝试不同的网站进行测试

4. **链接未完全移除**
   - 运行 `python test_link_removal.py` 验证功能
   - 检查是否有特殊格式的链接

### 日志查看

服务运行时会输出详细的日志信息，包括：
- 请求接收情况
- 处理进度
- 错误信息
- 链接移除状态

## 🔄 部署建议

### 生产环境部署

```bash
# 使用 gunicorn 部署
pip install gunicorn

gunicorn web_service:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:5000
```

### Docker 部署

可以结合项目主目录的 Docker 配置来部署此服务。

## 📝 许可证

本服务基于 Crawl4AI 项目构建，遵循相同的开源许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个服务！

## 🆕 更新日志

- **v1.0.0** - 初始版本
  - 基本网页抓取功能
  - Markdown 转换
  - **自动移除超链接功能**
  - 完整的测试套件

---

**享受使用 Crawl4AI Web Service! 🎉**

**🔗 特色功能：自动移除超链接，获得纯净的 Markdown 内容！** 