#!/usr/bin/env python3
"""
Playwright 增强打包钩子
确保playwright浏览器引擎和所有必要文件被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files, collect_dynamic_libs
import os
import sys

# 收集所有playwright子模块
hiddenimports = collect_submodules('playwright')

# 添加playwright核心模块
hiddenimports.extend([
    'playwright',
    'playwright.async_api',
    'playwright.sync_api',
    'playwright._impl',
    'playwright._impl._api_types',
    'playwright._impl._browser',
    'playwright._impl._browser_context',
    'playwright._impl._page',
    'playwright._impl._network',
    'playwright._impl._frame',
    'playwright._impl._element_handle',
    'playwright._impl._js_handle',
    'playwright._impl._helper',
    'playwright._impl._transport',
    'playwright._impl._connection',
])

# 收集playwright的数据文件
datas = collect_data_files('playwright')

# 收集动态库
binaries = collect_dynamic_libs('playwright')

# 尝试收集playwright浏览器二进制文件
try:
    import playwright
    playwright_path = os.path.dirname(playwright.__file__)
    driver_path = os.path.join(playwright_path, 'driver')
    if os.path.exists(driver_path):
        # 添加driver目录
        datas.append((driver_path, 'playwright/driver'))
        print("已添加playwright driver目录")
except Exception as e:
    print(f"Playwright driver收集警告: {e}")

print(f"Playwright打包钩子: 包含了 {len(hiddenimports)} 个模块, {len(datas)} 个数据文件, {len(binaries)} 个二进制文件") 