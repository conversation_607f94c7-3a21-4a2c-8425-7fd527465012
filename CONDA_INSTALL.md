# 📦 Conda 安装指南

本项目现在支持 conda 包管理器安装依赖。

## 🚀 快速开始

### 方法1: 使用 environment.yml (推荐)

```bash
# 创建新的conda环境并安装所有依赖
conda env create -f environment.yml

# 激活环境
conda activate crawl4ai-webservice

# 验证安装
python web_service.py
```

### 方法2: 分步安装 (推荐用于解决冲突)

```bash
# 创建新的conda环境
conda create -n crawl4ai-webservice python=3.8

# 激活环境
conda activate crawl4ai-webservice

# 先安装核心依赖 (避免版本冲突)
conda install -c conda-forge \
    aiosqlite lxml numpy pillow playwright \
    python-dotenv beautifulsoup4 aiofiles \
    colorama pydantic pyopenssl psutil nltk \
    rich cssselect brotli snowballstemmer \
    fastapi uvicorn

# 安装requests (使用较新版本)
conda install "requests>=2.31.0"

# 通过pip安装其余依赖
pip install litellm>=1.53.1 tf-playwright-stealth>=1.1.0 \
    xxhash>=3.4 rank-bm25>=0.2 chardet>=5.2.0

# 安装playwright浏览器
playwright install
```

### 方法3: 使用 requirements.txt (如果解决了冲突)

```bash
# 创建新的conda环境
conda create -n crawl4ai-webservice python=3.8

# 激活环境
conda activate crawl4ai-webservice

# 从conda安装依赖 (已修复版本冲突)
conda install --file requirements.txt

# 安装pip依赖
pip install litellm tf-playwright-stealth xxhash rank-bm25
```

## 📋 主要变更

- **版本约束语法**: `~=` 改为 `>=x.y,<x.z` 格式
- **包名调整**: `pyOpenSSL` 改为 `pyopenssl`
- **渠道优化**: 使用 `conda-forge` 获取最新版本
- **混合安装**: conda无法获取的包通过pip安装
- **冲突修复**: 调整了 requests/chardet 版本约束

## 🔧 环境管理

```bash
# 查看环境
conda env list

# 导出环境
conda env export > environment.yml

# 删除环境
conda env remove -n crawl4ai-webservice

# 更新环境
conda env update -f environment.yml
```

## ⚠️  常见问题解决

### 依赖冲突问题
如果遇到依赖冲突，推荐使用**方法2分步安装**：

```bash
# 如果environment.yml失败，使用分步安装
conda create -n crawl4ai-webservice python=3.8
conda activate crawl4ai-webservice

# 核心包
conda install -c conda-forge numpy lxml pillow requests beautifulsoup4
conda install -c conda-forge fastapi uvicorn pydantic aiofiles

# 其余通过pip
pip install -r <(grep -v "^#" requirements.txt | grep -E "(litellm|tf-playwright|xxhash|rank-bm25)")
```

### 特定包问题
1. **xxhash**: 只能通过pip安装
2. **tf-playwright-stealth**: 只能通过pip安装  
3. **chardet版本冲突**: 先安装低版本，再用pip升级

## 🧪 验证安装

```bash
# 测试导入
python -c "import crawl4ai, fastapi, uvicorn; print('All imports successful!')"

# 启动服务测试
python web_service.py

# 运行测试
python test_service.py
python test_api_format.py
```

## 💡 故障排除

1. **包冲突**: 删除环境重新创建，使用分步安装
2. **网络问题**: 使用国内镜像源
3. **权限问题**: 检查conda写入权限

```bash
# 添加国内镜像
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/

# 清理conda缓存
conda clean --all
```

## 🔄 如果仍有问题

使用最简化的方法：

```bash
# 最小化安装
conda create -n crawl4ai-webservice python=3.8
conda activate crawl4ai-webservice
conda install -c conda-forge fastapi uvicorn numpy requests beautifulsoup4

# 其余全部用pip
pip install aiosqlite lxml pillow playwright python-dotenv \
    aiofiles colorama pydantic pyopenssl psutil nltk rich \
    cssselect chardet brotli snowballstemmer litellm \
    tf-playwright-stealth xxhash rank-bm25

playwright install
``` 