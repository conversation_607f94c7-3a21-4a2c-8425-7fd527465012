#!/usr/bin/env python3
"""
使用Selenium连接现有Chrome浏览器的Google AI Studio自动化脚本
更简单的方法，不需要特殊的启动参数
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys


def setup_chrome_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    
    # 使用现有的Chrome用户数据
    # 这样可以保持登录状态
    import os
    user_data_dir = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data")
    chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
    chrome_options.add_argument("--profile-directory=Default")
    
    # 其他有用的选项
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        return driver
    except Exception as e:
        print(f"❌ 启动Chrome失败: {str(e)}")
        print("💡 请确保已安装ChromeDriver")
        print("   下载地址: https://chromedriver.chromium.org/")
        return None


def send_to_ai_studio_selenium(prompt: str):
    """
    使用Selenium发送提示词到Google AI Studio
    
    Args:
        prompt: 要发送的提示词
    """
    print("🚀 启动Chrome浏览器...")
    
    driver = setup_chrome_driver()
    if not driver:
        return False
    
    try:
        print("🌐 访问Google AI Studio...")
        driver.get("https://aistudio.google.com/prompts/new_chat")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 检查是否需要登录
        current_url = driver.current_url
        if "accounts.google.com" in current_url:
            print("🔑 需要登录Google账号")
            print("💡 请在浏览器中完成登录，然后按回车继续...")
            input("按回车键继续...")
            
            # 重新导航到AI Studio
            driver.get("https://aistudio.google.com/prompts/new_chat")
            time.sleep(5)
        
        print("📝 查找输入框...")
        
        # 多种方式查找输入框
        input_selectors = [
            "textarea[placeholder*='Enter a prompt']",
            "textarea[placeholder*='Type a message']",
            "textarea[data-testid='prompt-textarea']",
            "textarea[aria-label*='prompt']",
            "textarea:not([readonly]):not([disabled])",
            "div[contenteditable='true']",
            "[role='textbox']"
        ]
        
        input_element = None
        wait = WebDriverWait(driver, 10)
        
        for selector in input_selectors:
            try:
                input_element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                if input_element.is_displayed():
                    print(f"✅ 找到输入框: {selector}")
                    break
            except:
                continue
        
        if not input_element:
            # 尝试查找所有textarea
            textareas = driver.find_elements(By.TAG_NAME, "textarea")
            print(f"🔍 找到 {len(textareas)} 个textarea元素")
            for textarea in textareas:
                if textarea.is_displayed() and textarea.is_enabled():
                    input_element = textarea
                    print("✅ 使用可见的textarea")
                    break
        
        if not input_element:
            print("❌ 未找到输入框")
            return False
        
        # 滚动到输入框并聚焦
        driver.execute_script("arguments[0].scrollIntoView(true);", input_element)
        time.sleep(1)
        input_element.click()
        
        # 清空现有内容并填充新内容
        input_element.clear()
        time.sleep(0.5)
        
        print(f"📝 填充提示词 ({len(prompt)} 字符)...")
        input_element.send_keys(prompt)
        
        print("✅ 提示词填充完成")
        time.sleep(2)
        
        # 查找发送按钮
        print("🔍 查找发送按钮...")
        send_selectors = [
            "button[aria-label*='Send']",
            "button[title*='Send']",
            "button[data-testid='send-button']",
            "button[type='submit']",
            "button[aria-label*='发送']"
        ]
        
        send_button = None
        for selector in send_selectors:
            try:
                send_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                if send_button.is_displayed():
                    print(f"✅ 找到发送按钮: {selector}")
                    break
            except:
                continue
        
        if not send_button:
            # 通过文本内容查找按钮
            buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"🔍 找到 {len(buttons)} 个按钮")
            for button in buttons:
                try:
                    if not (button.is_displayed() and button.is_enabled()):
                        continue
                    
                    text_content = button.text.lower()
                    aria_label = (button.get_attribute('aria-label') or '').lower()
                    title = (button.get_attribute('title') or '').lower()
                    
                    if ('send' in text_content or 'send' in aria_label or 'send' in title or
                        '发送' in text_content or '发送' in aria_label):
                        send_button = button
                        print("✅ 通过文本找到发送按钮")
                        break
                except:
                    continue
        
        if not send_button:
            print("❌ 未找到发送按钮")
            print("💡 您可以手动点击发送按钮")
            print("🔍 浏览器将保持打开状态...")
            input("按回车键关闭浏览器...")
            return False
        
        # 点击发送按钮
        driver.execute_script("arguments[0].scrollIntoView(true);", send_button)
        time.sleep(0.5)
        
        print("🚀 点击发送按钮...")
        send_button.click()
        
        print("✅ 提示词发送成功！")
        print("⏳ 等待AI响应...")
        
        # 等待响应
        time.sleep(5)
        
        print("🎉 任务完成！")
        print("💡 请在浏览器中查看AI的响应")
        print("🔍 浏览器将保持打开状态，您可以查看完整响应...")
        print("⏳ 等待60秒后自动关闭，或按Ctrl+C提前退出")
        
        try:
            time.sleep(60)
        except KeyboardInterrupt:
            print("👋 用户中断，正在关闭...")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        return False
    
    finally:
        try:
            driver.quit()
        except:
            pass


def main():
    """主函数"""
    # 默认提示词
    default_prompt = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    # 检查命令行参数
    custom_prompt = None
    
    # 如果提供了自定义提示词文件
    if len(sys.argv) > 1 and not sys.argv[1].startswith("--"):
        try:
            with open(sys.argv[1], 'r', encoding='utf-8') as f:
                custom_prompt = f.read().strip()
            print(f"📄 从文件加载提示词: {sys.argv[1]}")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {sys.argv[1]}")
            return
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return
    
    prompt = custom_prompt or default_prompt
    
    print("🤖 Google AI Studio 自动化工具 (Selenium版)")
    print("="*60)
    print(f"📝 提示词长度: {len(prompt)} 字符")
    print("="*60)
    
    # 运行自动化
    success = send_to_ai_studio_selenium(prompt)
    
    if success:
        print("\n✅ 任务完成！")
    else:
        print("\n❌ 任务失败！")
        print("\n🔧 故障排除:")
        print("1. 确保已安装ChromeDriver")
        print("2. 确保Chrome浏览器已安装")
        print("3. 确保已登录Google AI Studio")
        sys.exit(1)


if __name__ == "__main__":
    main()
