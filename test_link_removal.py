#!/usr/bin/env python3
"""
测试链接移除功能的独立脚本
"""

import re


def remove_markdown_links(markdown_text: str) -> str:
    """
    移除markdown文本中的所有超链接
    
    Args:
        markdown_text: 原始markdown文本
        
    Returns:
        移除超链接后的markdown文本
    """
    if not markdown_text:
        return markdown_text
    
    # 移除图片链接 ![alt](url) -> alt (保留描述文字，移除感叹号)
    markdown_text = re.sub(r'!\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除普通链接 [text](url) -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用链接 [text][ref] -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除引用定义 [ref]: url
    markdown_text = re.sub(r'^\s*\[([^\]]+)\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除直接URL链接 <url>
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除自动链接（裸URL）
    markdown_text = re.sub(r'https?://\S+', '', markdown_text)
    
    # 清理多余的空行
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    
    return markdown_text.strip()


def test_link_removal():
    """测试链接移除功能"""
    
    # 测试用例
    test_cases = [
        {
            "name": "普通链接",
            "input": "这是一个 [链接文本](https://example.com) 在句子中。",
            "expected": "这是一个 链接文本 在句子中。"
        },
        {
            "name": "多个链接",
            "input": "访问 [Google](https://google.com) 和 [GitHub](https://github.com) 网站。",
            "expected": "访问 Google 和 GitHub 网站。"
        },
        {
            "name": "图片链接", 
            "input": "这是一张图片 ![图片描述](https://example.com/image.jpg) 在文本中。",
            "expected": "这是一张图片 图片描述 在文本中。"
        },
        {
            "name": "直接URL",
            "input": "访问 <https://example.com> 网站获取更多信息。",
            "expected": "访问  网站获取更多信息。"
        },
        {
            "name": "裸URL",
            "input": "网站地址是 https://example.com 请访问。",
            "expected": "网站地址是  请访问。"
        },
        {
            "name": "引用链接",
            "input": "这是 [引用链接][ref1] 和 [另一个][ref2]。\n\n[ref1]: https://example.com\n[ref2]: https://github.com",
            "expected": "这是 引用链接 和 另一个。"
        },
        {
            "name": "混合内容",
            "input": """# 标题
            
这是一段包含多种链接的文本：

- 普通链接：[Google](https://google.com)
- 图片：![Logo](https://example.com/logo.png)
- 直接链接：<https://github.com>
- 裸URL：https://stackoverflow.com

[reference]: https://example.com/ref

更多内容在这里。""",
            "expected_contains": ["Google", "Logo", "更多内容在这里"],
            "expected_not_contains": ["https://", "[", "](", "<http"]
        }
    ]
    
    print("🧪 测试链接移除功能")
    print("=" * 50)
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}:")
        
        input_text = test_case["input"]
        result = remove_markdown_links(input_text)
        
        print(f"   输入: {repr(input_text[:60])}...")
        print(f"   输出: {repr(result[:60])}...")
        
        if "expected" in test_case:
            if result == test_case["expected"]:
                print("   ✅ 通过")
                passed += 1
            else:
                print("   ❌ 失败")
                print(f"   期望: {repr(test_case['expected'])}")
                print(f"   实际: {repr(result)}")
        elif "expected_contains" in test_case and "expected_not_contains" in test_case:
            # 检查包含和不包含的内容
            contains_ok = all(item in result for item in test_case["expected_contains"])
            not_contains_ok = all(item not in result for item in test_case["expected_not_contains"])
            
            if contains_ok and not_contains_ok:
                print("   ✅ 通过")
                passed += 1
            else:
                print("   ❌ 失败")
                if not contains_ok:
                    missing = [item for item in test_case["expected_contains"] if item not in result]
                    print(f"   缺少内容: {missing}")
                if not not_contains_ok:
                    unwanted = [item for item in test_case["expected_not_contains"] if item in result]
                    print(f"   仍包含: {unwanted}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！链接移除功能工作正常。")
    else:
        print("⚠️  部分测试失败，需要检查链接移除逻辑。")
    
    return passed == total


if __name__ == "__main__":
    test_link_removal() 