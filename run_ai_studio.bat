@echo off
chcp 65001 >nul
echo 🤖 Google AI Studio 自动化工具
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查crawl4ai是否安装
python -c "import crawl4ai" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 警告: 未找到crawl4ai，正在安装...
    pip install crawl4ai
    if errorlevel 1 (
        echo ❌ 安装crawl4ai失败
        pause
        exit /b 1
    )
    echo ✅ crawl4ai安装成功
    
    echo 🔧 正在设置浏览器...
    crawl4ai-setup
    if errorlevel 1 (
        echo ⚠️ 浏览器设置可能有问题，尝试手动安装...
        python -m playwright install --with-deps chromium
    )
)

echo.
echo 请选择运行模式:
echo 1. 使用默认提示词（哈佛大学分析）
echo 2. 使用自定义提示词文件
echo 3. 运行测试
echo 4. 无头模式运行
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 🚀 启动默认模式...
    python quick_ai_studio.py
) else if "%choice%"=="2" (
    set /p filename=请输入提示词文件名: 
    if exist "%filename%" (
        echo 🚀 使用自定义提示词文件: %filename%
        python quick_ai_studio.py "%filename%"
    ) else (
        echo ❌ 文件不存在: %filename%
        pause
        exit /b 1
    )
) else if "%choice%"=="3" (
    echo 🧪 运行测试...
    python test_automation.py
) else if "%choice%"=="4" (
    echo 🚀 启动无头模式...
    python quick_ai_studio.py --headless
) else if "%choice%"=="5" (
    echo 👋 再见!
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo ✅ 操作完成
pause
