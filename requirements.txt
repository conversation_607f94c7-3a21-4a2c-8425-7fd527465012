# Note: These requirements are also specified in pyproject.toml
# This file is converted for conda compatibility
# Use: conda install --file requirements.txt
aiosqlite>=0.20,<0.21
lxml>=5.3,<5.4
litellm>=1.53.1
numpy>=1.26.0,<3
pillow>=10.4,<10.5
playwright>=1.49.0
python-dotenv>=1.0,<1.1
requests>=2.31.0
beautifulsoup4>=4.12,<4.13
aiofiles>=24.1.0
colorama>=0.4,<0.5
snowballstemmer>=2.2,<2.3
pydantic>=2.10
pyopenssl>=24.3.0
psutil>=6.1.1
nltk>=3.9.1
rich>=13.9.4
cssselect>=1.2.0
chardet>=5.2.0
brotli>=1.1.0
# Web service dependencies
fastapi>=0.104.0
uvicorn>=0.24.0
aiohttp>=3.9.0
# Note: xxhash, tf-playwright-stealth, rank-bm25 should be installed via pip