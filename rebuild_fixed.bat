@echo off
chcp 65001
echo ==========================================
echo Crawl4AI Web Service 修复版打包脚本
echo ==========================================

:: 设置环境变量
set PYTHONOPTIMIZE=1
set OMP_NUM_THREADS=1
set OPENBLAS_NUM_THREADS=1
set MKL_NUM_THREADS=1
set NUMPY_MADVISE_HUGEPAGE=0

echo.
echo 🔍 检查Python环境...
python --version
if %ERRORLEVEL% neq 0 (
    echo ❌ Python未找到！
    pause
    exit /b 1
)

echo.
echo 🔍 检查关键依赖...
python -c "import crawl4ai; print(f'✅ Crawl4AI版本: {crawl4ai.__version__}')" 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Crawl4AI未安装或有问题！
    pause
    exit /b 1
)

python -c "import playwright; print('✅ Playwright已安装')" 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Playwright未安装！
    pause
    exit /b 1
)

python -c "import numpy; print(f'✅ NumPy版本: {numpy.__version__}')" 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ NumPy未安装！
    pause
    exit /b 1
)

echo.
echo 🧹 清理旧文件...
if exist "dist\crawl4ai-web-service-fixed.exe" del "dist\crawl4ai-web-service-fixed.exe"
if exist "build" rmdir /s /q "build"

echo.
echo 🔨 开始打包...
pyinstaller --clean crawl4ai-no-nltk.spec

if %ERRORLEVEL% eq 0 (
    echo.
    echo ✅ 打包成功！
    echo 📍 可执行文件位置: dist\crawl4ai-web-service-fixed.exe
    echo.
    echo 🚀 测试运行exe...
    cd dist
    start "Crawl4AI Test" crawl4ai-web-service-fixed.exe
    cd ..
) else (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息。
)

echo.
echo 按任意键退出...
pause 