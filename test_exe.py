#!/usr/bin/env python3
"""
专门用于exe测试的脚本
在exe环境中运行crawl4ai诊断
"""

import sys
import os
import traceback
import asyncio
import time

def test_environment():
    """测试执行环境"""
    print("=" * 60)
    print("🔧 EXE环境测试")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    
    # 检查是否在PyInstaller环境中
    if hasattr(sys, '_MEIPASS'):
        print(f"✅ 检测到PyInstaller环境")
        print(f"临时目录: {sys._MEIPASS}")
    else:
        print("❌ 非PyInstaller环境")

def test_crawl4ai_basic():
    """基础crawl4ai测试"""
    print("\n" + "=" * 60)
    print("📋 基础Crawl4AI测试")
    print("=" * 60)
    
    try:
        print("步骤1: 导入crawl4ai...")
        import crawl4ai
        print(f"✅ crawl4ai导入成功: {crawl4ai}")
        
        print("步骤2: 获取版本...")
        try:
            from crawl4ai.__version__ import __version__
            print(f"✅ 版本: {__version__}")
        except Exception as e:
            print(f"❌ 版本获取失败: {e}")
        
        print("步骤3: 导入AsyncWebCrawler...")
        from crawl4ai import AsyncWebCrawler
        print("✅ AsyncWebCrawler导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        traceback.print_exc()
        return False

async def test_crawler_start():
    """测试crawler启动"""
    print("\n" + "=" * 60)
    print("🚀 Crawler启动测试")
    print("=" * 60)
    
    try:
        from crawl4ai import AsyncWebCrawler
        
        print("创建AsyncWebCrawler实例...")
        crawler = AsyncWebCrawler()
        print("✅ 实例创建成功")
        
        print("启动crawler...")
        await crawler.start()
        print("✅ Crawler启动成功")
        
        print(f"Ready状态: {crawler.ready}")
        
        print("关闭crawler...")
        await crawler.close()
        print("✅ Crawler关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Crawler启动测试失败: {e}")
        traceback.print_exc()
        return False

async def test_basic_crawl():
    """测试基础爬取功能"""
    print("\n" + "=" * 60)
    print("🌐 基础爬取测试")
    print("=" * 60)
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import CrawlerRunConfig
        
        # 使用本地HTML文件或简单URL
        test_html = """
        <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Hello World</h1>
            <p>This is a test page.</p>
        </body>
        </html>
        """
        
        async with AsyncWebCrawler() as crawler:
            print("✅ 上下文管理器创建成功")
            
            # 使用raw HTML测试
            test_url = f"raw:{test_html}"
            config = CrawlerRunConfig(verbose=True)
            
            print("开始爬取测试HTML...")
            result = await crawler.arun(url=test_url, config=config)
            
            print(f"爬取成功: {result.success}")
            if result.success:
                print(f"HTML长度: {len(result.html) if result.html else 0}")
                print("✅ 基础爬取测试成功")
                return True
            else:
                print(f"❌ 爬取失败: {getattr(result, 'error_message', '未知')}")
                return False
                
    except Exception as e:
        print(f"❌ 基础爬取测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 Crawl4AI EXE专用诊断工具")
    print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 环境检查
    test_environment()
    
    # 测试2: 基础功能
    basic_ok = test_crawl4ai_basic()
    
    if not basic_ok:
        print("\n❌ 基础测试失败，无法继续")
        input("按Enter键退出...")
        return
    
    # 测试3: 异步功能
    print("\n🔄 开始异步测试...")
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        start_ok = loop.run_until_complete(test_crawler_start())
        
        if start_ok:
            crawl_ok = loop.run_until_complete(test_basic_crawl())
            
            if crawl_ok:
                print("\n🎉 所有测试通过！EXE环境中Crawl4AI工作正常！")
            else:
                print("\n⚠️ 启动正常但爬取有问题")
        else:
            print("\n❌ Crawler启动失败")
            
    except Exception as e:
        print(f"\n💥 异步测试错误: {e}")
        traceback.print_exc()
    finally:
        loop.close()
    
    print(f"\n📊 测试完成: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    input("按Enter键退出...")

if __name__ == "__main__":
    main() 