#!/usr/bin/env python3
"""
直接测试UNC网站的cookie同意问题
"""

import requests
from bs4 import BeautifulSoup
import time

def test_unc_cookies():
    """测试UNC网站的cookie处理"""
    
    url = "https://asia.unc.edu/about/people/"
    print(f"🔍 测试URL: {url}")
    
    # 创建session
    session = requests.Session()
    
    # 设置真实的浏览器headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    session.headers.update(headers)
    
    # 预设cookie同意
    cookie_consent_values = [
        ('cookie_consent', 'true'),
        ('cookies_accepted', 'true'),
        ('gdpr_consent', 'true'),
        ('privacy_consent', 'true'),
        ('accept_cookies', 'true'),
        ('cookieconsent_status', 'allow'),
        ('cookie_notice_accepted', 'true'),
        ('eucookielaw', 'true'),
        ('unc_cookie_consent', 'accepted'),
        ('university_cookies', 'accepted'),
    ]
    
    print("🍪 设置预设cookies...")
    for name, value in cookie_consent_values:
        session.cookies.set(name, value, domain='.unc.edu')
        session.cookies.set(name, value)
    
    try:
        print("📡 发送第一次请求...")
        response = session.get(url, timeout=30, allow_redirects=True)
        print(f"✅ 状态码: {response.status_code}")
        print(f"📄 内容长度: {len(response.text)} 字符")
        print(f"🌐 最终URL: {response.url}")
        
        # 检查是否有cookie同意内容
        content_lower = response.text.lower()
        cookie_keywords = [
            'cookie consent', 'accept cookies', 'privacy notice', 
            'gdpr', 'cookie policy', 'privacy policy',
            'accept all cookies', 'continue to website',
            'i agree', 'consent to cookies'
        ]
        
        has_cookie_content = any(keyword in content_lower for keyword in cookie_keywords)
        print(f"🍪 是否检测到cookie相关内容: {has_cookie_content}")
        
        if has_cookie_content:
            print("🔍 检测到的cookie关键词:")
            for keyword in cookie_keywords:
                if keyword in content_lower:
                    print(f"  - {keyword}")
        
        # 检查是否包含实际内容（人员信息）
        people_keywords = ['dr.', 'professor', 'director', 'email', 'phone', 'staff', 'carolina asia center']
        has_people_content = any(keyword in content_lower for keyword in people_keywords)
        print(f"👥 是否包含人员信息: {has_people_content}")
        
        if has_people_content:
            print("👥 检测到的人员关键词:")
            for keyword in people_keywords:
                if keyword in content_lower:
                    print(f"  - {keyword}")
        
        # 解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找标题
        title = soup.find('title')
        print(f"📑 页面标题: {title.text.strip() if title else 'None'}")
        
        # 查找主要内容
        main_content = soup.find('main') or soup.find('div', class_='content') or soup.find('body')
        if main_content:
            text_content = main_content.get_text(strip=True)
            print(f"📝 主要内容长度: {len(text_content)} 字符")
            print(f"📝 内容预览: {text_content[:200]}...")
        
        # 查找所有人员信息
        people_sections = soup.find_all(['div', 'section'], text=lambda text: text and ('dr.' in text.lower() or 'professor' in text.lower()))
        print(f"👥 找到人员信息段落: {len(people_sections)}")
        
        return {
            'success': True,
            'status_code': response.status_code,
            'content_length': len(response.text),
            'has_cookie_content': has_cookie_content,
            'has_people_content': has_people_content,
            'title': title.text.strip() if title else None,
            'content_preview': text_content[:500] if 'text_content' in locals() else ''
        }
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return {'success': False, 'error': 'timeout'}
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP错误: {e}")
        return {'success': False, 'error': f'http_error: {e}'}
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return {'success': False, 'error': f'other: {e}'}

if __name__ == "__main__":
    result = test_unc_cookies()
    print("\n" + "="*50)
    print("📊 测试结果:")
    for key, value in result.items():
        print(f"  {key}: {value}") 