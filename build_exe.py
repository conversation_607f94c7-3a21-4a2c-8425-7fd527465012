#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Crawl4AI Web Service EXE 构建脚本
使用 PyInstaller 将 FastAPI 服务打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查并安装 PyInstaller"""
    try:
        import PyInstaller
        print("✓ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def get_hidden_imports():
    """获取需要隐藏导入的模块列表"""
    hidden_imports = [
        # FastAPI 相关
        'fastapi',
        'uvicorn',
        'uvicorn.lifespan',
        'uvicorn.lifespan.on',
        'uvicorn.protocols',
        'uvicorn.protocols.http',
        'uvicorn.protocols.websockets',
        'uvicorn.loops',
        'uvicorn.loops.auto',
        'starlette',
        'starlette.applications',
        'starlette.middleware',
        'starlette.responses',
        'starlette.routing',
        'pydantic',
        'pydantic.validators',
        
        # Crawl4AI 相关
        'crawl4ai',
        'crawl4ai.async_configs',
        'crawl4ai.AsyncWebCrawler',
        
        # HTTP 客户端
        'aiohttp',
        'aiohttp.client',
        'aiohttp.connector',
        'requests',
        
        # 浏览器自动化
        'playwright',
        'playwright.async_api',
        
        # 其他依赖
        'lxml',
        'beautifulsoup4',
        'bs4',
        'numpy',
        'pillow',
        'PIL',
        'dotenv',
        'aiosqlite',
        'litellm',
        'colorama',
        'snowballstemmer',
        'pyopenssl',
        'psutil',
        'nltk',
        'rich',
        'cssselect',
        'chardet',
        'brotli',
        'aiofiles',
        
        # 标准库模块
        'asyncio',
        'time',
        're',
        'typing',
        'json',
        'pathlib',
        'os',
        'sys'
    ]
    return hidden_imports

def get_datas():
    """获取需要包含的数据文件"""
    datas = []
    
    # 检查 crawl4ai 包中的数据文件
    try:
        import crawl4ai
        crawl4ai_path = Path(crawl4ai.__file__).parent
        
        # 包含 js_snippet 目录
        js_snippet_path = crawl4ai_path / "js_snippet"
        if js_snippet_path.exists():
            datas.append((str(js_snippet_path), "crawl4ai/js_snippet"))
            
        # 包含其他可能的数据文件
        for pattern in ["*.js", "*.json", "*.yml", "*.yaml"]:
            for file_path in crawl4ai_path.rglob(pattern):
                relative_path = file_path.relative_to(crawl4ai_path.parent)
                datas.append((str(file_path), str(relative_path.parent)))
                
    except ImportError:
        print("⚠️ 无法导入 crawl4ai，可能需要先安装")
    
    return datas

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    hidden_imports = get_hidden_imports()
    datas = get_datas()
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['web_service.py'],
    pathex=[],
    binaries=[],
    datas={datas},
    hiddenimports={hidden_imports},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='crawl4ai-web-service',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
'''
    
    with open('crawl4ai-web-service.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建了 PyInstaller 规格文件: crawl4ai-web-service.spec")

def build_exe():
    """构建可执行文件"""
    print("🚀 开始构建 EXE 文件...")
    
    # 检查依赖
    if not check_pyinstaller():
        return False
    
    # 创建规格文件
    create_spec_file()
    
    # 清理之前的构建文件
    for folder in ['build', 'dist']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"✓ 清理了 {folder} 目录")
    
    # 运行 PyInstaller
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "crawl4ai-web-service.spec"
        ]
        
        print("📦 正在打包...")
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ EXE 文件构建成功！")
            exe_path = Path("dist/crawl4ai-web-service.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 文件位置: {exe_path.absolute()}")
                print(f"📊 文件大小: {size_mb:.1f} MB")
                
                print("\n🎉 构建完成！")
                print("💡 使用方法:")
                print("   1. 双击运行 crawl4ai-web-service.exe")
                print("   2. 或在命令行中运行: ./dist/crawl4ai-web-service.exe")
                print("   3. 服务启动后访问: http://localhost:5000")
                print("   4. API 文档: http://localhost:5000/docs")
                return True
            else:
                print("❌ EXE 文件未生成")
                return False
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🛠️  Crawl4AI Web Service EXE 构建工具")
    print("=" * 60)
    
    # 检查主文件是否存在
    if not os.path.exists("web_service.py"):
        print("❌ 找不到 web_service.py 文件")
        print("请确保在项目根目录中运行此脚本")
        return False
    
    # 开始构建
    success = build_exe()
    
    if success:
        print("\n" + "=" * 60)
        print("🎊 构建成功完成！")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("💥 构建失败")
        print("=" * 60)
        print("💡 故障排除建议:")
        print("1. 确保所有依赖都已正确安装")
        print("2. 检查 requirements.txt 中的包是否都可以导入")
        print("3. 如果使用虚拟环境，确保已激活")
        print("4. 尝试先运行 python web_service.py 确认服务可以正常启动")
    
    return success

if __name__ == "__main__":
    main() 