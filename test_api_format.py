#!/usr/bin/env python3
"""
测试API响应格式是否完全符合要求
"""

import requests
import json


def test_api_response_format():
    """测试API响应格式"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 测试API响应格式")
    print("🎯 验证是否完全符合用户要求")
    print("=" * 50)
    
    # 测试用例1: 成功响应
    print("\n1️⃣ 测试成功响应格式...")
    test_data = {
        "url": "https://example.com",
        "timeout": 30
    }
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 请求成功")
            print("📋 响应字段检查:")
            
            # 检查必需字段
            required_fields = ['success', 'url', 'markdown_content', 'processing_time', 'error']
            for field in required_fields:
                if field in result:
                    print(f"   ✅ {field}: {type(result[field]).__name__}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
            
            # 验证字段类型和值
            checks = []
            checks.append(("success是布尔值", isinstance(result.get('success'), bool)))
            checks.append(("url是字符串", isinstance(result.get('url'), str)))
            checks.append(("markdown_content是字符串", isinstance(result.get('markdown_content'), str)))
            checks.append(("processing_time是数字", isinstance(result.get('processing_time'), (int, float))))
            checks.append(("error是字符串", isinstance(result.get('error'), str)))
            
            if result.get('success'):
                checks.append(("成功时error为空", result.get('error') == ""))
                checks.append(("成功时有markdown内容", len(result.get('markdown_content', '')) > 0))
            else:
                checks.append(("失败时error不为空", len(result.get('error', '')) > 0))
            
            print("\n🔍 字段验证:")
            for check_name, check_result in checks:
                print(f"   {'✅' if check_result else '❌'} {check_name}")
                
            # 显示示例响应
            print(f"\n📄 示例响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False)[:300] + "...")
            
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    # 测试用例2: 错误响应（无效URL）
    print("\n2️⃣ 测试错误响应格式...")
    error_test_data = {
        "url": "https://invalid-domain-that-does-not-exist.xyz",
        "timeout": 10
    }
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=error_test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("📋 错误响应格式检查:")
            
            # 验证错误响应
            if not result.get('success'):
                print("   ✅ success为false")
                
                if result.get('error'):
                    print(f"   ✅ error字段包含错误信息: {result['error'][:50]}...")
                else:
                    print("   ❌ error字段为空")
                
                if not result.get('markdown_content'):
                    print("   ✅ markdown_content为空（符合要求）")
                else:
                    print("   ❌ markdown_content不为空")
                    
                print(f"   ✅ processing_time: {result.get('processing_time')}秒")
                
            else:
                print("   ⚠️  返回成功但测试的是无效URL")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误测试异常: {str(e)}")


def test_api_specification():
    """测试API规格说明"""
    
    base_url = "http://localhost:5000"
    
    print("\n3️⃣ 测试API规格说明...")
    
    try:
        response = requests.get(base_url)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 根路径响应成功")
            
            if 'response_format' in result:
                print("✅ 包含响应格式说明")
                print("📋 API格式说明:")
                for field, desc in result['response_format'].items():
                    print(f"   - {field}: {desc}")
            else:
                print("❌ 缺少响应格式说明")
                
        else:
            print(f"❌ 根路径访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API规格测试异常: {str(e)}")


if __name__ == "__main__":
    print("🚀 API响应格式完整测试")
    print("🎯 验证是否符合用户要求规范")
    print("=" * 60)
    
    print("📌 用户要求的响应格式:")
    print("""
{
  "success": true/false, 
  "url": "https://asia.unc.edu/about/people", 
  "markdown_content": "网页内容markdown",
  "processing_time": 3.005,
  "error": "异常信息"  // 如果出错或超时，返回到这里
}
    """)
    
    test_api_response_format()
    test_api_specification()
    
    print("\n" + "=" * 60)
    print("🎉 API格式测试完成!")
    print("💡 请检查上述所有检查项是否都显示 ✅") 