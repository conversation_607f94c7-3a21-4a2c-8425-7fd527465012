#!/usr/bin/env python3
"""
简化的Crawl4AI测试脚本
专门用于诊断exe环境中的初始化问题
"""

import sys
import os
import traceback
import asyncio

def test_imports():
    """测试关键模块导入"""
    print("=" * 50)
    print("🔍 测试模块导入...")
    print("=" * 50)
    
    modules = [
        "numpy", "playwright", "crawl4ai", "fake_useragent",
        "fake_http_header", "playwright_stealth", "aiohttp", "bs4"
    ]
    
    for module_name in modules:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', '未知')
            print(f"✅ {module_name}: {version}")
        except Exception as e:
            print(f"❌ {module_name}: {e}")

def test_crawl4ai_version():
    """测试crawl4ai版本获取"""
    print("\n" + "=" * 50)
    print("📋 测试Crawl4AI版本...")
    print("=" * 50)
    
    try:
        # 方法1：从主模块获取
        import crawl4ai
        print(f"✅ crawl4ai模块路径: {crawl4ai.__file__}")
        
        # 方法2：从版本文件获取
        try:
            from crawl4ai.__version__ import __version__
            print(f"✅ 从__version__模块获取: {__version__}")
        except Exception as e:
            print(f"❌ 从__version__模块获取失败: {e}")
        
        # 方法3：检查模块属性
        if hasattr(crawl4ai, '__version__'):
            print(f"✅ 从模块属性获取: {crawl4ai.__version__}")
        else:
            print("❌ 模块没有__version__属性")
            
    except Exception as e:
        print(f"❌ crawl4ai导入失败: {e}")
        traceback.print_exc()

async def test_crawler_creation():
    """测试Crawler创建和启动"""
    print("\n" + "=" * 50)
    print("🚀 测试Crawler创建和启动...")
    print("=" * 50)
    
    try:
        # 步骤1：导入AsyncWebCrawler
        print("步骤1: 导入AsyncWebCrawler...")
        from crawl4ai import AsyncWebCrawler
        print("✅ AsyncWebCrawler导入成功")
        
        # 步骤2：创建实例
        print("步骤2: 创建AsyncWebCrawler实例...")
        crawler = AsyncWebCrawler()
        print("✅ AsyncWebCrawler实例创建成功")
        
        # 步骤3：启动crawler
        print("步骤3: 启动crawler...")
        await crawler.start()
        print("✅ Crawler启动成功！")
        
        # 步骤4：检查ready状态
        print(f"步骤4: Crawler ready状态: {crawler.ready}")
        
        # 步骤5：关闭crawler
        print("步骤5: 关闭crawler...")
        await crawler.close()
        print("✅ Crawler关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Crawler测试失败: {e}")
        traceback.print_exc()
        return False

async def test_simple_crawl():
    """测试简单的网页爬取"""
    print("\n" + "=" * 50)
    print("🌐 测试简单网页爬取...")
    print("=" * 50)
    
    try:
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import CrawlerRunConfig
        
        async with AsyncWebCrawler() as crawler:
            print("✅ Crawler上下文管理器创建成功")
            
            # 使用一个简单的测试URL
            test_url = "https://httpbin.org/html"
            config = CrawlerRunConfig(
                page_timeout=10000,  # 10秒超时
                wait_until="domcontentloaded",
                verbose=True
            )
            
            print(f"开始爬取: {test_url}")
            result = await crawler.arun(url=test_url, config=config)
            
            print(f"爬取结果 - 成功: {result.success}")
            if result.success:
                print(f"状态码: {getattr(result, 'status_code', '未知')}")
                print(f"HTML长度: {len(result.html) if result.html else 0}")
                print("✅ 简单爬取测试成功！")
                return True
            else:
                print(f"❌ 爬取失败: {getattr(result, 'error_message', '未知错误')}")
                return False
                
    except Exception as e:
        print(f"❌ 简单爬取测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 Crawl4AI EXE环境诊断工具")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    
    # 测试1：模块导入
    test_imports()
    
    # 测试2：版本获取
    test_crawl4ai_version()
    
    # 测试3：Crawler创建和启动
    print("\n🔄 开始异步测试...")
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        crawler_ok = loop.run_until_complete(test_crawler_creation())
        
        if crawler_ok:
            # 测试4：简单爬取
            crawl_ok = loop.run_until_complete(test_simple_crawl())
            
            if crawl_ok:
                print("\n🎉 所有测试通过！Crawl4AI在当前环境中工作正常。")
            else:
                print("\n⚠️ Crawler启动正常，但爬取功能有问题。")
        else:
            print("\n❌ Crawler启动失败，需要检查依赖和配置。")
            
    except Exception as e:
        print(f"\n💥 异步测试过程中发生错误: {e}")
        traceback.print_exc()
    finally:
        loop.close()
    
    print("\n📊 诊断完成！")
    input("按Enter键退出...")

if __name__ == "__main__":
    main() 