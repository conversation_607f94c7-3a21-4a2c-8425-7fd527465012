# 🚀 Crawl4AI Web Service 启动指南

## 📋 环境要求

- Python 3.8+
- conda 或 pip 包管理器
- 网络连接

## 🔧 环境配置

### 方法1: 使用conda (推荐)

```bash
# 使用环境配置文件
conda env create -f environment.yml
conda activate crawl4ai-webservice

# 或者使用修复脚本
chmod +x fix_conda_install.sh
./fix_conda_install.sh
```

### 方法2: 使用pip

```bash
# 创建虚拟环境
python -m venv crawl4ai-env
source crawl4ai-env/bin/activate  # Linux/Mac
# 或者
crawl4ai-env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install litellm tf-playwright-stealth xxhash rank-bm25
```

### 方法3: 分步安装 (如果有问题)

```bash
# 安装核心依赖
pip install fastapi uvicorn aiosqlite lxml numpy pillow
pip install requests beautifulsoup4 aiofiles colorama pydantic
pip install psutil nltk rich cssselect chardet brotli

# 安装playwright并下载浏览器
pip install playwright
playwright install

# 安装其余依赖
pip install litellm tf-playwright-stealth xxhash rank-bm25
```

## 🚀 启动服务

### 基本启动

```bash
# 确保在项目目录中
cd /path/to/crawl4ai-main/crawl4ai-main

# 启动服务
python web_service.py
```

### 后台启动

```bash
# Linux/Mac 后台启动
nohup python web_service.py > service.log 2>&1 &

# 查看日志
tail -f service.log
```

### 自定义配置启动

```bash
# 修改端口和主机（编辑web_service.py最后几行）
# 默认配置：
# uvicorn.run(app, host="0.0.0.0", port=5000, log_level="info")

# 启动
python web_service.py
```

## 📡 验证服务

### 1. 检查服务状态

```bash
# 访问根路径
curl http://localhost:5000/

# 健康检查
curl http://localhost:5000/health
```

### 2. 测试核心功能

```bash
# 测试markdown接口
curl -X POST "http://localhost:5000/markdown" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com", "timeout": 30}'
```

### 3. 运行测试脚本

```bash
# 基础功能测试
python test_service.py

# API格式测试
python test_api_format.py

# UNC页面测试
python test_real_unc_page.py

# 错误处理测试
python test_error_response.py
```

## 🌐 访问服务

启动成功后，可以通过以下方式访问：

- **服务主页**: http://localhost:5000
- **API文档**: http://localhost:5000/docs (Swagger UI)
- **ReDoc文档**: http://localhost:5000/redoc
- **健康检查**: http://localhost:5000/health

## 📋 API使用示例

### Python客户端

```python
import requests

response = requests.post(
    "http://localhost:5000/markdown",
    json={
        "url": "https://asia.unc.edu/about/people",
        "timeout": 60
    }
)

result = response.json()
print(f"成功: {result['success']}")
print(f"内容长度: {len(result['markdown_content'])}")
```

### curl命令

```bash
curl -X POST "http://localhost:5000/markdown" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://asia.unc.edu/about/people",
    "timeout": 60
  }'
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :5000
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **依赖缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   ```

3. **playwright浏览器缺失**
   ```bash
   # 安装浏览器
   playwright install
   ```

4. **权限问题**
   ```bash
   # 给脚本执行权限
   chmod +x *.py
   ```

### 调试模式

```bash
# 开启调试模式（修改web_service.py）
uvicorn.run(
    app, 
    host="0.0.0.0", 
    port=5000,
    log_level="debug",  # 改为debug
    reload=True         # 自动重载
)
```

## 🎯 功能特性

- ✅ 网页内容抓取转Markdown
- ✅ 自动移除超链接
- ✅ 反爬虫干扰文本清理
- ✅ 完整的错误处理
- ✅ 符合规范的API响应格式
- ✅ 自动生成API文档

## 📞 技术支持

如果遇到问题：

1. 查看启动日志
2. 运行测试脚本诊断
3. 检查网络连接
4. 确认依赖版本 