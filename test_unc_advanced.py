#!/usr/bin/env python3
"""
高级反反爬虫测试 - 针对UNC网站的安全检测
"""

import requests
from bs4 import BeautifulSoup
import time
import random
import json
from urllib.parse import urljoin

def advanced_unc_test():
    """高级反反爬虫测试"""
    
    url = "https://asia.unc.edu/about/people/"
    print(f"🚀 高级测试URL: {url}")
    
    # 随机延迟模拟人类行为
    time.sleep(random.uniform(2, 5))
    
    # 创建session
    session = requests.Session()
    
    # 更真实的浏览器指纹
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
    ]
    
    selected_ua = random.choice(user_agents)
    print(f"🔍 使用User-Agent: {selected_ua[:50]}...")
    
    # 完整的浏览器headers
    headers = {
        'User-Agent': selected_ua,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }
    
    session.headers.update(headers)
    
    # 设置realistic的cookies
    session.cookies.set('visited', 'true')
    session.cookies.set('session_start', str(int(time.time())))
    
    try:
        # 策略1: 先访问主页建立信任
        print("🏠 策略1: 访问主页建立信任...")
        main_page = "https://asia.unc.edu/"
        main_response = session.get(main_page, timeout=30, allow_redirects=True)
        print(f"   主页状态码: {main_response.status_code}")
        
        # 随机延迟
        time.sleep(random.uniform(3, 7))
        
        # 策略2: 模拟从主页点击链接
        print("🔗 策略2: 模拟从主页导航...")
        headers['Referer'] = main_page
        session.headers.update(headers)
        
        response = session.get(url, timeout=30, allow_redirects=True)
        print(f"✅ 目标页面状态码: {response.status_code}")
        
        if response.status_code == 403:
            print("❌ 仍然被阻止，尝试策略3...")
            
            # 策略3: 更改请求模式
            time.sleep(random.uniform(5, 10))
            
            # 添加更多真实的headers
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache',
            })
            
            # 移除可能被检测的headers
            if 'sec-ch-ua' in headers:
                del headers['sec-ch-ua']
            if 'sec-ch-ua-mobile' in headers:
                del headers['sec-ch-ua-mobile'] 
            if 'sec-ch-ua-platform' in headers:
                del headers['sec-ch-ua-platform']
            if 'Sec-Fetch-Dest' in headers:
                del headers['Sec-Fetch-Dest']
            if 'Sec-Fetch-Mode' in headers:
                del headers['Sec-Fetch-Mode']
            if 'Sec-Fetch-Site' in headers:
                del headers['Sec-Fetch-Site']
            if 'Sec-Fetch-User' in headers:
                del headers['Sec-Fetch-User']
            
            session.headers.clear()
            session.headers.update(headers)
            
            response = session.get(url, timeout=30, allow_redirects=True)
            print(f"✅ 策略3后状态码: {response.status_code}")
        
        if response.status_code == 403:
            print("❌ 策略3失败，尝试策略4 - 使用代理模式...")
            
            # 策略4: 模拟学术访问
            time.sleep(random.uniform(8, 15))
            
            academic_headers = {
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Referer': 'https://www.google.com/',
            }
            
            session.headers.clear()
            session.headers.update(academic_headers)
            
            response = session.get(url, timeout=30, allow_redirects=True)
            print(f"✅ 策略4后状态码: {response.status_code}")
        
        # 分析响应
        print(f"📄 最终内容长度: {len(response.text)} 字符")
        print(f"🌐 最终URL: {response.url}")
        
        if response.status_code == 200:
            # 成功！解析内容
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找标题
            title = soup.find('title')
            print(f"📑 页面标题: {title.text.strip() if title else 'None'}")
            
            # 查找人员信息
            people_keywords = ['dr.', 'professor', 'director', 'email', 'phone', 'staff']
            content_lower = response.text.lower()
            has_people_content = any(keyword in content_lower for keyword in people_keywords)
            print(f"👥 包含人员信息: {has_people_content}")
            
            if has_people_content:
                print("👥 检测到的关键词:")
                for keyword in people_keywords:
                    if keyword in content_lower:
                        count = content_lower.count(keyword)
                        print(f"  - {keyword}: {count}次")
            
            # 提取实际内容
            main_content = soup.find('main') or soup.find('div', class_='content') or soup.find('body')
            if main_content:
                text_content = main_content.get_text(strip=True)
                print(f"📝 主要内容长度: {len(text_content)} 字符")
                
                # 保存成功的内容到文件
                with open('unc_success_content.txt', 'w', encoding='utf-8') as f:
                    f.write(text_content)
                print("💾 内容已保存到 unc_success_content.txt")
                
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'has_people_content': has_people_content,
                    'title': title.text.strip() if title else None,
                    'content_preview': text_content[:500]
                }
        else:
            # 失败分析
            soup = BeautifulSoup(response.content, 'html.parser')
            title = soup.find('title')
            error_content = soup.get_text(strip=True)
            
            print(f"❌ 失败分析:")
            print(f"   状态码: {response.status_code}")
            print(f"   标题: {title.text.strip() if title else 'None'}")
            print(f"   错误内容: {error_content[:300]}...")
            
            return {
                'success': False,
                'status_code': response.status_code,
                'error': f'Status {response.status_code}',
                'error_content': error_content[:500]
            }
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return {'success': False, 'error': 'timeout'}
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return {'success': False, 'error': f'exception: {e}'}

if __name__ == "__main__":
    result = advanced_unc_test()
    print("\n" + "="*60)
    print("📊 高级测试结果:")
    for key, value in result.items():
        print(f"  {key}: {value}") 