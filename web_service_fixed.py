#!/usr/bin/env python3
"""
Crawl4AI Web Service - 修复版本
解决exe环境中crawler初始化问题
"""

import sys
import os
import time
import re
import traceback
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.openapi.docs import get_swagger_ui_html
from pydantic import BaseModel, HttpUrl
import uvicorn

def remove_markdown_links(markdown_text: str) -> str:
    """移除Markdown文本中的所有超链接，保留链接文本"""
    if not markdown_text:
        return ""
    
    # 移除引用式链接定义（如：[1]: http://example.com "Title"）
    markdown_text = re.sub(r'^\s*\[.+?\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除内联链接，保留链接文本（如：[文本](http://example.com) -> 文本）
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用式链接，保留链接文本（如：[文本][1] -> 文本）
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除自动链接（如：<http://example.com>）
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除纯URL链接
    markdown_text = re.sub(r'(?<![\[\(])(https?://\S+)(?![\]\)])', '', markdown_text)
    
    # 清理多余的空行和空格
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    markdown_text = re.sub(r'[ \t]+', ' ', markdown_text)
    
    return markdown_text.strip()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    url: HttpUrl
    timeout: int = 60


class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    url: str
    markdown_content: str
    processing_time: float
    error: str = ""


# 全局crawler实例
global_crawler = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global global_crawler
    
    # 启动时初始化
    try:
        print("🔄 正在初始化Crawl4AI...")
        
        # 检查是否为exe环境
        is_exe = getattr(sys, 'frozen', False)
        if is_exe:
            print("🔍 检测到exe环境，尝试使用备用方案...")
            # 在exe环境中，playwright可能无法正常工作
            # 这里我们设置一个标记，后续使用备用抓取方案
            global_crawler = "EXE_MODE"
            print("✅ 已设置exe模式抓取方案")
        else:
            # 导入必要的模块
            from crawl4ai import AsyncWebCrawler
            from crawl4ai.async_configs import BrowserConfig
            
            # 创建浏览器配置
            browser_config = BrowserConfig(
                headless=True,
                verbose=False
            )
            
            # 创建crawler实例
            global_crawler = AsyncWebCrawler(config=browser_config)
            
            # 显式启动crawler
            await global_crawler.start()
            print("✅ Crawl4AI初始化成功！")
        
    except Exception as e:
        print(f"❌ Crawl4AI初始化失败: {e}")
        print("🔄 尝试启用备用抓取方案...")
        global_crawler = "FALLBACK_MODE"
        print("✅ 备用抓取方案已启用")
    
    yield
    
    # 关闭时清理
    if global_crawler and isinstance(global_crawler, object) and hasattr(global_crawler, 'close'):
        try:
            await global_crawler.close()
            print("✅ Crawl4AI已关闭")
        except Exception as e:
            print(f"❌ 关闭Crawl4AI时出错: {e}")

# 创建FastAPI应用
app = FastAPI(
    title="Crawl4AI Web Service Fixed",
    description="修复版本 - 解决exe环境问题",
    version="1.0.1-fixed",
    docs_url=None,
    redoc_url=None,
    lifespan=lifespan
)

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui.css",
    )

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    global global_crawler
    
    if global_crawler == "EXE_MODE":
        crawler_status = "exe模式"
    elif global_crawler == "FALLBACK_MODE":
        crawler_status = "备用模式"
    elif global_crawler and hasattr(global_crawler, 'ready'):
        crawler_status = "已初始化" if global_crawler.ready else "未就绪"
    else:
        crawler_status = "未初始化"
    
    return {
        "message": "Crawl4AI Web Service Fixed",
        "version": "1.0.1-fixed",
        "crawler_status": crawler_status,
        "description": "修复版本 - 解决exe环境中的初始化问题",
        "endpoints": {
            "/markdown": "POST - 网页内容抓取并转换为Markdown格式",
            "/status": "GET - 查看服务状态"
        }
    }

@app.get("/status")
async def status_check():
    """服务状态检查"""
    global global_crawler
    
    status_info = {
        "service": "running",
        "crawler_mode": str(global_crawler) if isinstance(global_crawler, str) else "normal",
        "crawler_initialized": global_crawler is not None,
        "python_version": sys.version,
        "working_directory": os.getcwd(),
        "is_exe": getattr(sys, 'frozen', False)
    }
    
    if isinstance(global_crawler, str):
        status_info["crawler_ready"] = True  # 备用模式始终就绪
        status_info["test_crawl"] = "fallback_mode_ready"
    elif global_crawler and hasattr(global_crawler, 'ready'):
        status_info["crawler_ready"] = global_crawler.ready
        # 测试基本功能
        try:
            if global_crawler.ready:
                from crawl4ai.async_configs import CrawlerRunConfig
                test_html = "<html><body><h1>Test</h1></body></html>"
                config = CrawlerRunConfig(verbose=False)
                
                test_result = await global_crawler.arun(url=f"raw:{test_html}", config=config)
                # 修复：arun返回生成器，需要获取第一个结果
                if hasattr(test_result, '__aiter__'):
                    async for result in test_result:
                        status_info["test_crawl"] = "success" if result.success else "failed"
                        break
                else:
                    status_info["test_crawl"] = "success" if test_result.success else "failed"
            else:
                status_info["test_crawl"] = "crawler_not_ready"
        except Exception as e:
            status_info["test_crawl"] = f"error: {str(e)}"
    else:
        status_info["crawler_ready"] = False
        status_info["test_crawl"] = "crawler_not_initialized"
    
    return status_info

async def fallback_crawl(url: str, timeout: int = 60):
    """备用爬取方案，使用requests + BeautifulSoup"""
    try:
        import requests
        from bs4 import BeautifulSoup
        import html2text
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 移除脚本和样式标签
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 转换为markdown
        h = html2text.HTML2Text()
        h.ignore_links = False
        h.ignore_images = True
        markdown_content = h.handle(str(soup))
        
        return {
            "success": True,
            "markdown_content": markdown_content,
            "error": ""
        }
        
    except Exception as e:
        return {
            "success": False,
            "markdown_content": "",
            "error": f"备用抓取失败: {str(e)}"
        }

@app.post("/markdown", response_model=CrawlResponse)
async def crawl_to_markdown_fixed(request: CrawlRequest):
    """
    修复版本的爬取接口，支持exe环境
    """
    global global_crawler
    start_time = time.time()
    
    # 检查crawler模式
    if global_crawler == "EXE_MODE" or global_crawler == "FALLBACK_MODE":
        # 使用备用抓取方案
        print(f"🔄 使用备用方案抓取: {request.url}")
        fallback_result = await fallback_crawl(str(request.url), request.timeout)
        
        processing_time = round(time.time() - start_time, 3)
        
        if fallback_result["success"]:
            clean_markdown = remove_markdown_links(fallback_result["markdown_content"])
            return CrawlResponse(
                success=True,
                url=str(request.url),
                markdown_content=clean_markdown,
                processing_time=processing_time,
                error=""
            )
        else:
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=fallback_result["error"]
            )
    
    # 原有的crawl4ai逻辑
    if not global_crawler:
        return CrawlResponse(
            success=False,
            url=str(request.url),
            markdown_content="",
            processing_time=round(time.time() - start_time, 3),
            error="Crawl4AI未初始化，请检查服务状态"
        )
    
    if not global_crawler.ready:
        return CrawlResponse(
            success=False,
            url=str(request.url),
            markdown_content="",
            processing_time=round(time.time() - start_time, 3),
            error="Crawl4AI未就绪，请重启服务"
        )
    
    try:
        from crawl4ai.async_configs import CrawlerRunConfig
        
        # 配置爬取参数
        timeout_ms = request.timeout * 1000
        config = CrawlerRunConfig(
            page_timeout=timeout_ms,
            wait_until="domcontentloaded",
            verbose=False
        )
        
        # 执行爬取
        crawl_result = await global_crawler.arun(
            url=str(request.url),
            config=config
        )
        
        # 修复：处理arun返回的生成器
        result = None
        if hasattr(crawl_result, '__aiter__'):
            async for res in crawl_result:
                result = res
                break
        else:
            result = crawl_result
        
        processing_time = round(time.time() - start_time, 3)
        
        if result and result.success:
            # 处理markdown内容
            raw_markdown = ""
            if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                raw_markdown = result.markdown.raw_markdown or ""
            
            clean_markdown = remove_markdown_links(raw_markdown)
            
            return CrawlResponse(
                success=True,
                url=str(request.url),
                markdown_content=clean_markdown,
                processing_time=processing_time,
                error=""
            )
        else:
            error_msg = getattr(result, 'error_message', '爬取失败') if result else "爬取失败"
            
            # 简化错误信息
            if "timeout" in error_msg.lower() or "exceeded" in error_msg.lower():
                error_msg = f"抓取超时：{request.timeout}秒"
            elif "connection" in error_msg.lower() or "network" in error_msg.lower():
                error_msg = "网络连接失败"
            elif "403" in error_msg or "forbidden" in error_msg.lower():
                error_msg = "访问被拒绝"
            elif len(error_msg) > 200:
                error_msg = "网页抓取失败"
            
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=error_msg
            )
            
    except Exception as e:
        processing_time = round(time.time() - start_time, 3)
        error_str = str(e)
        
        # 处理各种异常
        if "timeout" in error_str.lower():
            error_msg = f"抓取超时：{request.timeout}秒"
        elif "connection" in error_str.lower() or "network" in error_str.lower():
            error_msg = "网络连接失败"
        elif "403" in error_str or "forbidden" in error_str.lower():
            error_msg = "访问被拒绝"
        else:
            error_msg = "处理过程中发生错误，无法完成网页内容抓取"
        
        return CrawlResponse(
            success=False,
            url=str(request.url),
            markdown_content="",
            processing_time=processing_time,
            error=error_msg
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    global global_crawler
    
    if isinstance(global_crawler, str):
        # 备用模式或exe模式
        status = "healthy"
    elif global_crawler and hasattr(global_crawler, 'ready'):
        status = "healthy" if global_crawler.ready else "unhealthy"
    else:
        status = "unhealthy"
    
    return {
        "status": status,
        "service": "crawl4ai-web-service-fixed",
        "mode": str(global_crawler) if isinstance(global_crawler, str) else "normal"
    }

if __name__ == "__main__":
    print("🚀 启动 Crawl4AI Web Service (修复版)...")
    print("📡 服务将在 http://localhost:5000 运行")
    print("📚 API文档可在 http://localhost:5000/docs 查看")
    print("🔍 /markdown 接口用于网页内容抓取")
    print("📊 /status 接口用于检查服务状态")
    print("🔗 自动移除markdown内容中的超链接")
    print("🛠️  支持exe环境的备用抓取方案")
    print("=" * 50)
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=5000,
        log_level="info"
    ) 