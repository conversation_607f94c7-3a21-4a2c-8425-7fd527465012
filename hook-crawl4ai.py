#!/usr/bin/env python3
"""
Crawl4AI 专用打包钩子
确保crawl4ai包的所有模块和数据文件被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files, collect_dynamic_libs

# 收集所有crawl4ai子模块
hiddenimports = collect_submodules('crawl4ai')

# 添加可能缺失的crawl4ai模块
hiddenimports.extend([
    'crawl4ai',
    'crawl4ai.async_webcrawler',
    'crawl4ai.async_configs',
    'crawl4ai.AsyncWebCrawler',
    'crawl4ai.content_filter_strategy',
    'crawl4ai.browser_manager',
    'crawl4ai.async_crawler_strategy',
    'crawl4ai.extraction_strategy',
    'crawl4ai.chunking_strategy',
    'crawl4ai.cache_context',
    'crawl4ai.utils',
    'crawl4ai.models',
    'crawl4ai.config',
])

# 收集crawl4ai的数据文件
datas = collect_data_files('crawl4ai')

# 收集动态库
binaries = collect_dynamic_libs('crawl4ai')

print(f"Crawl4AI打包钩子: 包含了 {len(hiddenimports)} 个模块, {len(datas)} 个数据文件, {len(binaries)} 个二进制文件") 