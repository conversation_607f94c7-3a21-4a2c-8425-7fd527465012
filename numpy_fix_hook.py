#!/usr/bin/env python3
"""
NumPy 修复钩子 - 简化版本
解决 PyInstaller 打包后 "CPU dispatcher tracer already initialized" 错误
"""

import os
import sys

# 在任何导入之前设置关键环境变量
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['VECLIB_MAXIMUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

# 禁用 Intel MKL 的 CPU 调度程序
os.environ['MKL_THREADING_LAYER'] = 'SEQUENTIAL'

# 设置 NumPy 相关环境变量
os.environ['NUMPY_DISABLE_CPU_FEATURES'] = 'ALL'

# 禁用NumPy的多线程和高级特性
os.environ['NUMPY_EXPERIMENTAL_ARRAY_FUNCTION'] = '0'
os.environ['NUMPY_DISABLE_BLAS_THREADING'] = '1'

# 强制使用单线程BLAS
os.environ['GOTO_NUM_THREADS'] = '1'
os.environ['ATLAS_NUM_THREADS'] = '1'

# 防止CPU特性检测和优化
os.environ['NUMPY_DISABLE_CPU_FEATURES'] = 'SSE,SSE2,SSE3,SSSE3,SSE41,SSE42,AVX,AVX2,AVX512F'

# 设置Python优化标志
os.environ['PYTHONOPTIMIZE'] = '1'

# 防止numpy多重初始化
os.environ['NUMPY_NO_CONFIG'] = '1'

#print("NumPy 修复钩子已应用（简化版）") 