#!/usr/bin/env python3
"""
连接到现有Chrome浏览器的Google AI Studio自动化脚本
使用已经登录的浏览器会话，避免重复登录
"""

import asyncio
import sys
import json
from playwright.async_api import async_playwright


async def connect_to_existing_chrome(prompt: str):
    """
    连接到现有的Chrome浏览器并发送提示词
    
    Args:
        prompt: 要发送的提示词
    """
    print("🔗 连接到现有Chrome浏览器...")
    print("💡 请确保Chrome浏览器已启动并开启远程调试")
    print("   启动命令: chrome.exe --remote-debugging-port=9222")
    print("   或者在已打开的Chrome中访问: chrome://settings/")
    
    async with async_playwright() as p:
        try:
            # 连接到现有的Chrome浏览器
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")
            print("✅ 成功连接到Chrome浏览器")
            
            # 获取所有页面
            contexts = browser.contexts
            if not contexts:
                print("❌ 没有找到浏览器上下文")
                return False
            
            context = contexts[0]
            pages = context.pages
            
            # 查找Google AI Studio页面或使用现有页面
            ai_studio_page = None
            for page in pages:
                url = page.url
                if "aistudio.google.com" in url:
                    ai_studio_page = page
                    print(f"✅ 找到AI Studio页面: {url}")
                    break
                elif "accounts.google.com" in url and "aistudio" in url:
                    # 这是登录页面，重定向到AI Studio
                    ai_studio_page = page
                    print(f"🔑 找到Google登录页面，将导航到AI Studio")
                    break

            if not ai_studio_page:
                print("🌐 创建新的AI Studio页面...")
                ai_studio_page = await context.new_page()

            # 导航到AI Studio页面
            print("🌐 导航到Google AI Studio...")
            await ai_studio_page.goto("https://aistudio.google.com/prompts/new_chat")
            print("⏳ 等待页面加载...")
            await ai_studio_page.wait_for_load_state("domcontentloaded")
            await asyncio.sleep(8)  # 增加等待时间

            # 检查是否需要登录
            current_url = ai_studio_page.url
            if "accounts.google.com" in current_url:
                print("🔑 需要登录Google账号")
                print("💡 请在浏览器中完成登录，然后按回车继续...")
                input("按回车键继续...")

                # 重新导航到AI Studio
                await ai_studio_page.goto("https://aistudio.google.com/prompts/new_chat")
                await ai_studio_page.wait_for_load_state("domcontentloaded")
                await asyncio.sleep(5)
            
            # 切换到AI Studio页面
            await ai_studio_page.bring_to_front()
            
            print("📝 开始填充提示词...")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 查找输入框
            input_selectors = [
                'textarea[placeholder*="Enter a prompt"]',
                'textarea[placeholder*="Type a message"]',
                'textarea[data-testid="prompt-textarea"]',
                'textarea[aria-label*="prompt"]',
                'textarea:not([readonly]):not([disabled])',
                'div[contenteditable="true"]',
                '[role="textbox"]'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await ai_studio_page.query_selector(selector)
                    if input_element:
                        is_visible = await input_element.is_visible()
                        if is_visible:
                            print(f"✅ 找到输入框: {selector}")
                            break
                except Exception as e:
                    continue
            
            if not input_element:
                # 尝试查找所有textarea
                textareas = await ai_studio_page.query_selector_all('textarea')
                print(f"🔍 找到 {len(textareas)} 个textarea元素")
                for textarea in textareas:
                    is_visible = await textarea.is_visible()
                    is_enabled = await textarea.is_enabled()
                    if is_visible and is_enabled:
                        input_element = textarea
                        print("✅ 使用可见的textarea")
                        break
            
            if not input_element:
                print("❌ 未找到输入框")
                return False
            
            # 滚动到输入框并聚焦
            await input_element.scroll_into_view_if_needed()
            await input_element.focus()
            
            # 清空现有内容并填充新内容
            await input_element.fill("")  # 清空
            await asyncio.sleep(0.5)
            await input_element.fill(prompt)  # 填充
            
            print(f"✅ 已填充提示词 ({len(prompt)} 字符)")
            
            # 等待一下让输入生效
            await asyncio.sleep(2)
            
            # 查找发送按钮
            send_selectors = [
                'button[aria-label*="Send"]',
                'button[title*="Send"]',
                'button[data-testid="send-button"]',
                'button[type="submit"]',
                'button[aria-label*="发送"]'
            ]
            
            send_button = None
            for selector in send_selectors:
                try:
                    send_button = await ai_studio_page.query_selector(selector)
                    if send_button:
                        is_visible = await send_button.is_visible()
                        is_enabled = await send_button.is_enabled()
                        if is_visible and is_enabled:
                            print(f"✅ 找到发送按钮: {selector}")
                            break
                except Exception as e:
                    continue
            
            if not send_button:
                # 通过文本内容查找按钮
                buttons = await ai_studio_page.query_selector_all('button')
                print(f"🔍 找到 {len(buttons)} 个按钮")
                for button in buttons:
                    try:
                        is_visible = await button.is_visible()
                        is_enabled = await button.is_enabled()
                        if not (is_visible and is_enabled):
                            continue
                        
                        text_content = await button.text_content()
                        aria_label = await button.get_attribute('aria-label')
                        title = await button.get_attribute('title')
                        
                        text_content = (text_content or '').lower()
                        aria_label = (aria_label or '').lower()
                        title = (title or '').lower()
                        
                        if ('send' in text_content or 'send' in aria_label or 'send' in title or
                            '发送' in text_content or '发送' in aria_label):
                            send_button = button
                            print("✅ 通过文本找到发送按钮")
                            break
                    except Exception as e:
                        continue
            
            if not send_button:
                print("❌ 未找到发送按钮")
                return False
            
            # 点击发送按钮
            await send_button.scroll_into_view_if_needed()
            await send_button.click()
            print("✅ 已点击发送按钮")
            
            print("🎉 提示词发送成功！")
            print("💡 请在浏览器中查看AI的响应")
            print("🔍 浏览器将保持打开状态，您可以查看完整响应...")
            
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            print("💡 请确保Chrome浏览器已启动并开启远程调试")
            print("   启动命令: chrome.exe --remote-debugging-port=9222")
            return False


def main():
    """主函数"""
    # 默认提示词
    default_prompt = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    # 检查命令行参数
    custom_prompt = None
    
    # 如果提供了自定义提示词文件
    if len(sys.argv) > 1 and not sys.argv[1].startswith("--"):
        try:
            with open(sys.argv[1], 'r', encoding='utf-8') as f:
                custom_prompt = f.read().strip()
            print(f"📄 从文件加载提示词: {sys.argv[1]}")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {sys.argv[1]}")
            return
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return
    
    prompt = custom_prompt or default_prompt
    
    print("🤖 Google AI Studio 自动化工具 (现有浏览器版)")
    print("="*60)
    print(f"📝 提示词长度: {len(prompt)} 字符")
    print("="*60)
    
    # 运行自动化
    success = asyncio.run(connect_to_existing_chrome(prompt))
    
    if success:
        print("\n✅ 任务完成！")
    else:
        print("\n❌ 任务失败！")
        print("\n🔧 故障排除:")
        print("1. 确保Chrome浏览器已启动")
        print("2. 启动Chrome时添加参数: --remote-debugging-port=9222")
        print("3. 确保已登录Google AI Studio")
        sys.exit(1)


if __name__ == "__main__":
    main()
