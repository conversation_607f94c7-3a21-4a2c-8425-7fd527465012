#!/usr/bin/env python3
"""
Crawl4AI Web Service 测试脚本
用于测试 /markdown 接口的功能，包括移除超链接功能
"""

import requests
import json
import time


def test_markdown_endpoint():
    """测试 /markdown 接口"""
    
    # 服务URL
    base_url = "http://localhost:5000"
    
    # 测试数据
    test_data = {
        "url": "https://asia.unc.edu/about/people",
        "timeout": 60
    }
    
    print("🧪 开始测试 Crawl4AI Web Service")
    print(f"📡 服务地址: {base_url}")
    print(f"🎯 测试URL: {test_data['url']}")
    print(f"⏱️  超时设置: {test_data['timeout']}秒")
    print("🔗 测试移除超链接功能")
    print("-" * 50)
    
    try:
        # 发送POST请求到 /markdown 接口
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        request_time = round(time.time() - start_time, 3)
        
        print(f"📥 HTTP状态码: {response.status_code}")
        print(f"⏱️  请求耗时: {request_time}秒")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 请求成功!")
            print(f"🎯 URL: {result['url']}")
            print(f"✔️  成功状态: {result['success']}")
            print(f"⏱️  处理时长: {result['processing_time']}秒")
            print(f"📄 内容长度: {len(result['markdown_content'])} 字符")
            print(f"❌ 错误信息: {result['error'] if result['error'] else '无'}")
            
            # 检查响应格式是否符合要求
            required_fields = ['success', 'url', 'markdown_content', 'processing_time', 'error']
            missing_fields = [field for field in required_fields if field not in result]
            if missing_fields:
                print(f"⚠️  缺少字段: {missing_fields}")
            else:
                print("✅ 响应格式完整符合要求!")
            
            # 对于成功的响应，error应该为空
            if result['success'] and result['error']:
                print("⚠️  成功响应但error字段不为空")
            elif not result['success'] and not result['error']:
                print("⚠️  失败响应但error字段为空")
            else:
                print("✅ 错误字段状态正确!")
            
            # 检查是否包含链接和干扰文本
            content = result['markdown_content']
            if content:  # 只有成功时才检查内容
                link_patterns = [
                    r'\[([^\]]*)\]\([^)]*\)',  # [text](url)
                    r'<https?://[^>]+>',       # <url>
                    r'https?://\S+',           # 裸URL
                ]
                
                links_found = []
                for pattern in link_patterns:
                    import re
                    matches = re.findall(pattern, content)
                    if matches:
                        links_found.extend(matches)
                
                # 检查干扰文本
                has_interference = "hsdfsf" in content.lower()
                
                if links_found:
                    print("❌ 仍然包含链接:")
                    for link in links_found[:5]:  # 只显示前5个
                        print(f"   - {link}")
                else:
                    print("✅ 成功移除所有超链接!")
                
                if has_interference:
                    print("❌ 仍然包含反爬虫干扰文本")
                else:
                    print("✅ 成功移除反爬虫干扰文本!")
                
                # 显示部分内容（前500字符）
                print("\n📝 Markdown内容预览:")
                print("-" * 30)
                print(content[:500])
                if len(content) > 500:
                    print("... (内容已截断)")
                print("-" * 30)
            else:
                print("📝 内容为空（符合失败响应要求）")
            
            return True
            
        else:
            print("❌ 请求失败!")
            try:
                error_result = response.json()
                print(f"错误详情: {error_result}")
                
                # 检查失败响应格式
                if 'error' in error_result and error_result['error']:
                    print("✅ 失败响应包含正确的error字段!")
                else:
                    print("⚠️  失败响应缺少error字段信息")
                    
            except:
                print(f"原始错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保服务正在运行")
        print("💡 启动服务: python web_service.py")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
        return False
        
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        return False


def test_simple_page():
    """测试一个包含很多链接的简单页面"""
    
    base_url = "http://localhost:5000"
    
    # 测试一个包含很多链接的页面
    test_data = {
        "url": "https://example.com",
        "timeout": 30
    }
    
    print("\n🧪 测试简单页面的链接移除...")
    print(f"🎯 测试URL: {test_data['url']}")
    print("-" * 30)
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 处理成功 ({result['processing_time']}秒)")
            print(f"📄 内容长度: {len(result['markdown_content'])} 字符")
            
            # 检查链接移除效果
            content = result['markdown_content']
            
            # 检查常见的链接模式
            has_brackets = '[' in content and '](' in content
            has_angle_brackets = '<http' in content
            has_bare_urls = 'http://' in content or 'https://' in content
            
            print("🔗 链接检查结果:")
            print(f"   - Markdown链接 [text](url): {'❌ 仍存在' if has_brackets else '✅ 已移除'}")
            print(f"   - 角括号链接 <url>: {'❌ 仍存在' if has_angle_brackets else '✅ 已移除'}")
            print(f"   - 裸URL: {'❌ 仍存在' if has_bare_urls else '✅ 已移除'}")
            
            return not (has_brackets or has_angle_brackets or has_bare_urls)
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return False


def test_health_endpoint():
    """测试健康检查接口"""
    
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{base_url}/health")
        
        if response.status_code == 200:
            result = response.json()
            print(f"🏥 健康检查: {result['status']}")
            return True
        else:
            print("❌ 健康检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查错误: {str(e)}")
        return False


def test_root_endpoint():
    """测试根路径接口"""
    
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(base_url)
        
        if response.status_code == 200:
            result = response.json()
            print(f"🏠 服务信息: {result['message']}")
            print(f"📋 版本: {result['version']}")
            print(f"🔗 新特性: {result['description']}")
            return True
        else:
            print("❌ 根路径测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 根路径测试错误: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 Crawl4AI Web Service 完整测试")
    print("🔗 包含超链接移除功能测试")
    print("=" * 60)
    
    # 测试根路径
    print("\n1️⃣ 测试根路径...")
    test_root_endpoint()
    
    # 测试健康检查
    print("\n2️⃣ 测试健康检查...")
    test_health_endpoint()
    
    # 测试简单页面
    print("\n3️⃣ 测试简单页面的链接移除...")
    simple_success = test_simple_page()
    
    # 测试主要功能
    print("\n4️⃣ 测试核心功能...")
    main_success = test_markdown_endpoint()
    
    print("\n" + "=" * 60)
    if main_success and simple_success:
        print("🎉 所有测试通过!")
        print("✅ 超链接移除功能工作正常!")
    else:
        print("⚠️  部分测试失败，请检查服务状态")
    
    print("\n💡 使用说明:")
    print("1. 启动服务: python web_service.py")
    print("2. 查看API文档: http://localhost:5000/docs")
    print("3. 运行测试: python test_service.py")
    print("4. 新功能: 自动移除markdown中的所有超链接") 