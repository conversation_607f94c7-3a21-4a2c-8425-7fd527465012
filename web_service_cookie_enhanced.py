#!/usr/bin/env python3
"""
Crawl4AI Web Service - Cookie增强版
专门处理需要cookie同意和JavaScript渲染的网站
"""

import asyncio
import time
import re
import sys
import os
import random
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional
from pydantic import BaseModel, HttpUrl
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
import uvicorn


def remove_markdown_links(markdown_text: str) -> str:
    """移除markdown文本中的所有超链接和反爬虫干扰文本"""
    if not markdown_text:
        return markdown_text
    
    # 移除常见的反爬虫干扰文本
    markdown_text = re.sub(r'hsdfsf', '', markdown_text, flags=re.IGNORECASE)
    
    # 移除其他常见的干扰模式
    markdown_text = re.sub(r'(?i)[a-z]{5,}(?=[a-z]*@)', 
                          lambda m: '' if len(set(m.group())) <= 2 else m.group(), 
                          markdown_text)
    
    # 修复被干扰的邮箱地址格式
    markdown_text = re.sub(r'(\w+)@(\w+)\.(\w+)', r'\1@\2.\3', markdown_text)
    
    # 移除图片链接 ![alt](url) -> alt
    markdown_text = re.sub(r'!\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除普通链接 [text](url) -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用链接 [text][ref] -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除引用定义 [ref]: url
    markdown_text = re.sub(r'^\s*\[([^\]]+)\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除直接URL链接 <url>
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除自动链接（裸URL）
    markdown_text = re.sub(r'https?://\S+', '', markdown_text)
    
    # 清理多余的空行
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    
    # 清理多余的空格
    markdown_text = re.sub(r'[ ]{3,}', '  ', markdown_text)
    
    return markdown_text.strip()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    url: HttpUrl
    timeout: int = 60
    accept_cookies: bool = True  # 新增：是否自动接受cookies
    wait_for_content: bool = True  # 新增：是否等待动态内容加载


class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    url: str
    markdown_content: str
    processing_time: float
    error: str = ""
    method_used: str = ""
    cookies_handled: bool = False  # 新增：是否处理了cookies


# 全局状态
crawler_state = {
    "primary_crawler": None,
    "mode": "INIT",  # INIT, PRIMARY, FALLBACK, ERROR
    "init_attempts": 0,
    "max_init_attempts": 3
}


async def cookie_aware_crawl(url: str, timeout: int = 60, accept_cookies: bool = True, wait_for_content: bool = True) -> Dict[str, Any]:
    """Cookie感知的爬取方案，专门处理需要cookie同意的网站"""
    try:
        import requests
        from bs4 import BeautifulSoup
        import html2text
        
        # 创建持久化session
        session = requests.Session()
        
        # 轮换User-Agent - 更真实的浏览器标识
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        
        session.headers.update(headers)
        
        # 设置重试机制
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        cookies_handled = False
        
        # 如果启用cookie处理，先预设常见的cookie同意
        if accept_cookies:
            # 常见的cookie同意cookies
            common_consent_cookies = {
                'cookie_consent': 'true',
                'cookies_accepted': 'true',
                'gdpr_consent': 'true',
                'privacy_consent': 'true',
                'accept_cookies': 'true',
                'cookieconsent_status': 'allow',
                'cookie_notice_accepted': 'true',
                'eucookielaw': 'true',
                'CookieConsent': '{stamp:%27-1%27,necessary:true,preferences:true,statistics:true,marketing:true,method:%27explicit%27,ver:1}',
                # UNC网站可能使用的cookies
                'unc_cookie_consent': 'accepted',
                'university_cookies': 'accepted',
            }
            
            for cookie_name, cookie_value in common_consent_cookies.items():
                session.cookies.set(cookie_name, cookie_value, domain='.unc.edu')
                session.cookies.set(cookie_name, cookie_value)
            
            cookies_handled = True
        
        # 第一次请求 - 可能触发cookie同意页面
        print(f"🍪 正在访问: {url}")
        response = session.get(url, timeout=timeout, allow_redirects=True)
        response.raise_for_status()
        
        # 检查是否遇到cookie同意页面
        content_lower = response.text.lower()
        cookie_keywords = [
            'cookie consent', 'accept cookies', 'privacy notice', 
            'gdpr', 'cookie policy', 'privacy policy',
            'accept all cookies', 'continue to website',
            'i agree', 'consent to cookies'
        ]
        
        has_cookie_consent = any(keyword in content_lower for keyword in cookie_keywords)
        
        if has_cookie_consent and accept_cookies:
            print("🍪 检测到cookie同意页面，尝试自动接受...")
            
            # 解析页面，寻找同意按钮或表单
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 寻找常见的同意按钮
            consent_selectors = [
                'button[id*="accept"]', 'button[class*="accept"]',
                'button[id*="consent"]', 'button[class*="consent"]',
                'button[id*="agree"]', 'button[class*="agree"]',
                'button[id*="continue"]', 'button[class*="continue"]',
                'a[href*="accept"]', 'a[class*="accept"]',
                'input[type="submit"][value*="accept"]',
                'input[type="submit"][value*="agree"]',
                'input[type="button"][value*="accept"]',
            ]
            
            consent_button = None
            for selector in consent_selectors:
                consent_button = soup.select_one(selector)
                if consent_button:
                    break
            
            # 尝试找到并"点击"同意按钮
            if consent_button:
                print("🍪 找到同意按钮，模拟点击...")
                
                # 如果是表单，尝试提交
                form = consent_button.find_parent('form')
                if form:
                    form_action = form.get('action')
                    # 处理可能的list类型
                    if isinstance(form_action, list):
                        form_action = form_action[0] if form_action else ''
                    elif form_action is None:
                        form_action = ''
                    
                    if not form_action.startswith('http'):
                        from urllib.parse import urljoin
                        form_action = urljoin(url, form_action)
                    
                    form_data = {}
                    for input_tag in form.find_all('input'):
                        name = input_tag.get('name')
                        value = input_tag.get('value', '')
                        if name:
                            form_data[name] = value
                    
                    try:
                        response = session.post(form_action, data=form_data, timeout=timeout)
                        response.raise_for_status()
                        print("🍪 成功提交cookie同意表单")
                    except Exception as e:
                        print(f"🍪 提交表单失败，继续使用原始响应: {e}")
            
            # 等待一下让JavaScript处理cookie
            if wait_for_content:
                import time
                time.sleep(2)
            
            # 重新请求页面，现在应该显示完整内容
            try:
                response = session.get(url, timeout=timeout, allow_redirects=True)
                response.raise_for_status()
                print("🍪 Cookie处理后重新获取页面内容")
            except Exception as e:
                print(f"🍪 重新请求失败，使用原始内容: {e}")
        
        # 检查内容类型
        content_type = response.headers.get('content-type', '').lower()
        if 'html' not in content_type:
            return {
                "success": False,
                "markdown_content": "",
                "error": f"不支持的内容类型: {content_type}",
                "cookies_handled": cookies_handled
            }
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 移除脚本和样式标签
        for script in soup(["script", "style", "noscript"]):
            script.decompose()
        
        # 移除cookie同意相关的元素
        cookie_consent_selectors = [
            '[id*="cookie"]', '[class*="cookie"]',
            '[id*="consent"]', '[class*="consent"]',
            '[id*="gdpr"]', '[class*="gdpr"]',
            '[id*="privacy"]', '[class*="privacy"]',
        ]
        
        for selector in cookie_consent_selectors:
            for element in soup.select(selector):
                # 只移除可能是弹窗或通知的元素
                if any(keyword in element.get_text().lower() for keyword in ['cookie', 'consent', 'privacy', 'gdpr']):
                    element.decompose()
        
        # 移除注释
        from bs4 import Comment
        comments = soup.findAll(text=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
        
        # 转换为markdown
        h = html2text.HTML2Text()
        h.ignore_links = False
        h.ignore_images = True
        h.body_width = 0  # 不限制行宽
        h.unicode_snob = True
        h.bypass_tables = False  # 保留表格结构
        markdown_content = h.handle(str(soup))
        
        return {
            "success": True,
            "markdown_content": markdown_content,
            "error": "",
            "cookies_handled": cookies_handled
        }
        
    except requests.exceptions.Timeout:
        return {
            "success": False,
            "markdown_content": "",
            "error": f"请求超时：{timeout}秒",
            "cookies_handled": cookies_handled
        }
    except requests.exceptions.HTTPError as e:
        status_code = e.response.status_code if e.response else 0
        if status_code == 403:
            return {
                "success": False,
                "markdown_content": "",
                "error": "访问被拒绝：网站可能有高级反爬虫保护或需要登录",
                "cookies_handled": cookies_handled
            }
        elif status_code == 404:
            return {
                "success": False,
                "markdown_content": "",
                "error": "页面不存在",
                "cookies_handled": cookies_handled
            }
        else:
            return {
                "success": False,
                "markdown_content": "",
                "error": f"HTTP错误 {status_code}: {str(e)}",
                "cookies_handled": cookies_handled
            }
    except Exception as e:
        error_msg = str(e)
        if "connection" in error_msg.lower():
            error_msg = "网络连接失败"
        elif "ssl" in error_msg.lower():
            error_msg = "SSL证书验证失败"
        return {
            "success": False,
            "markdown_content": "",
            "error": f"Cookie感知抓取失败: {error_msg}",
            "cookies_handled": cookies_handled
        }


async def init_primary_crawler():
    """初始化主爬虫"""
    global crawler_state
    
    try:
        # 检测是否在exe环境中
        is_exe = getattr(sys, 'frozen', False)
        if is_exe:
            print("🔍 检测到exe环境，使用Cookie增强模式")
            crawler_state["mode"] = "COOKIE_ENHANCED"
            return False
        
        # 尝试导入并初始化crawl4ai
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        crawler = AsyncWebCrawler(config=browser_config)
        await crawler.start()
        
        crawler_state["primary_crawler"] = crawler
        crawler_state["mode"] = "PRIMARY"
        print("✅ Crawl4AI主爬虫初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 主爬虫初始化失败: {e}")
        crawler_state["mode"] = "COOKIE_ENHANCED"
        return False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global crawler_state
    
    print("🚀 启动 Crawl4AI Web Service Cookie Enhanced...")
    
    # 尝试初始化主爬虫
    success = await init_primary_crawler()
    
    if success:
        print("📡 服务模式: 完整功能 (Crawl4AI + Cookie增强)")
    else:
        print("📡 服务模式: Cookie增强模式 (requests + Cookie处理)")
    
    print("📚 服务地址: http://localhost:5000")
    print("📚 API文档: http://localhost:5000/docs")
    
    yield
    
    # 清理资源
    if crawler_state["primary_crawler"]:
        try:
            await crawler_state["primary_crawler"].close()
            print("✅ 主爬虫已关闭")
        except Exception as e:
            print(f"❌ 关闭主爬虫时出错: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="Crawl4AI Web Service Cookie Enhanced",
    description="Cookie增强版网页内容抓取服务，专门处理需要cookie同意的网站",
    version="2.1.0",
    docs_url=None,
    redoc_url=None,
    lifespan=lifespan
)


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url or "/openapi.json",
        title=app.title + " - API文档",
        swagger_js_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui.css",
    )


@app.get("/")
async def root():
    """根路径，返回服务信息"""
    global crawler_state
    
    return {
        "service": "Crawl4AI Web Service Cookie Enhanced",
        "version": "2.1.0",
        "crawler_mode": crawler_state["mode"],
        "description": "Cookie增强版网页内容抓取服务，专门处理需要cookie同意和JavaScript渲染的网站",
        "endpoints": {
            "/markdown": "POST - 网页内容抓取并转换为Markdown格式",
            "/status": "GET - 查看详细服务状态",
            "/health": "GET - 健康检查"
        },
        "features": [
            "智能cookie同意处理",
            "JavaScript渲染网站支持",
            "高级反反爬虫机制",
            "自动表单提交",
            "链接清理和内容优化"
        ]
    }


@app.get("/status")
async def detailed_status():
    """详细的服务状态检查"""
    global crawler_state
    
    status = {
        "service_status": "running",
        "crawler_mode": crawler_state["mode"],
        "primary_crawler_available": crawler_state["primary_crawler"] is not None,
        "cookie_enhanced_available": True,
        "init_attempts": crawler_state["init_attempts"],
        "python_version": sys.version,
        "is_exe_environment": getattr(sys, 'frozen', False),
        "working_directory": os.getcwd()
    }
    
    return status


@app.post("/markdown", response_model=CrawlResponse)
async def crawl_to_markdown_cookie_enhanced(request: CrawlRequest):
    """
    Cookie增强版爬取接口，专门处理需要cookie同意的网站
    """
    global crawler_state
    start_time = time.time()
    
    # 使用Cookie增强方案
    if crawler_state["mode"] in ["COOKIE_ENHANCED", "FALLBACK"]:
        print(f"🍪 使用Cookie增强方案抓取: {request.url}")
        
        cookie_result = await cookie_aware_crawl(
            str(request.url), 
            request.timeout, 
            request.accept_cookies,
            request.wait_for_content
        )
        processing_time = round(time.time() - start_time, 3)
        
        if cookie_result["success"]:
            clean_markdown = remove_markdown_links(cookie_result["markdown_content"])
            return CrawlResponse(
                success=True,
                url=str(request.url),
                markdown_content=clean_markdown,
                processing_time=processing_time,
                error="",
                method_used="cookie_enhanced",
                cookies_handled=cookie_result.get("cookies_handled", False)
            )
        else:
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=cookie_result["error"],
                method_used="cookie_enhanced",
                cookies_handled=cookie_result.get("cookies_handled", False)
            )
    
    # 尝试使用主爬虫（如果可用）
    if crawler_state["mode"] == "PRIMARY" and crawler_state["primary_crawler"]:
        try:
            from crawl4ai.async_configs import CrawlerRunConfig
            
            timeout_ms = request.timeout * 1000
            config = CrawlerRunConfig(
                page_timeout=timeout_ms,
                wait_until="domcontentloaded",
                verbose=False
            )
            
            crawl_result = await crawler_state["primary_crawler"].arun(
                url=str(request.url),
                config=config
            )
            
            # 处理arun返回的生成器
            result = None
            if hasattr(crawl_result, '__aiter__'):
                async for res in crawl_result:
                    result = res
                    break
            else:
                result = crawl_result
            
            processing_time = round(time.time() - start_time, 3)
            
            if result and result.success:
                raw_markdown = ""
                if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                    raw_markdown = result.markdown.raw_markdown or ""
                
                clean_markdown = remove_markdown_links(raw_markdown)
                
                return CrawlResponse(
                    success=True,
                    url=str(request.url),
                    markdown_content=clean_markdown,
                    processing_time=processing_time,
                    error="",
                    method_used="crawl4ai",
                    cookies_handled=False
                )
            else:
                # 主爬虫失败，自动降级到Cookie增强方案
                print(f"⚠️ 主爬虫失败，自动降级到Cookie增强方案: {request.url}")
                cookie_result = await cookie_aware_crawl(
                    str(request.url), 
                    request.timeout,
                    request.accept_cookies,
                    request.wait_for_content
                )
                
                if cookie_result["success"]:
                    clean_markdown = remove_markdown_links(cookie_result["markdown_content"])
                    return CrawlResponse(
                        success=True,
                        url=str(request.url),
                        markdown_content=clean_markdown,
                        processing_time=processing_time,
                        error="主抓取失败，已使用Cookie增强方案",
                        method_used="auto_cookie_fallback",
                        cookies_handled=cookie_result.get("cookies_handled", False)
                    )
                else:
                    error_msg = getattr(result, 'error_message', '抓取失败') if result else "抓取失败"
                    return CrawlResponse(
                        success=False,
                        url=str(request.url),
                        markdown_content="",
                        processing_time=processing_time,
                        error=f"主抓取和Cookie增强抓取均失败: {error_msg}",
                        method_used="both_failed",
                        cookies_handled=cookie_result.get("cookies_handled", False)
                    )
        
        except Exception as e:
            # 主爬虫异常，自动降级到Cookie增强方案
            print(f"⚠️ 主爬虫异常，自动降级到Cookie增强方案: {e}")
            cookie_result = await cookie_aware_crawl(
                str(request.url), 
                request.timeout,
                request.accept_cookies,
                request.wait_for_content
            )
            processing_time = round(time.time() - start_time, 3)
            
            if cookie_result["success"]:
                clean_markdown = remove_markdown_links(cookie_result["markdown_content"])
                return CrawlResponse(
                    success=True,
                    url=str(request.url),
                    markdown_content=clean_markdown,
                    processing_time=processing_time,
                    error="主抓取异常，已使用Cookie增强方案",
                    method_used="exception_cookie_fallback",
                    cookies_handled=cookie_result.get("cookies_handled", False)
                )
            else:
                return CrawlResponse(
                    success=False,
                    url=str(request.url),
                    markdown_content="",
                    processing_time=processing_time,
                    error=f"主抓取异常且Cookie增强抓取失败: {str(e)}",
                    method_used="both_failed",
                    cookies_handled=cookie_result.get("cookies_handled", False)
                )
    
    # 如果到这里，说明没有可用的抓取方法
    processing_time = round(time.time() - start_time, 3)
    return CrawlResponse(
        success=False,
        url=str(request.url),
        markdown_content="",
        processing_time=processing_time,
        error="没有可用的抓取方法",
        method_used="none",
        cookies_handled=False
    )


@app.get("/health")
async def health_check():
    """健康检查接口"""
    global crawler_state
    
    if crawler_state["mode"] in ["PRIMARY", "COOKIE_ENHANCED"]:
        status = "healthy"
    else:
        status = "unhealthy"
    
    return {
        "status": status,
        "service": "crawl4ai-web-service-cookie-enhanced",
        "mode": crawler_state["mode"],
        "timestamp": int(time.time())
    }


if __name__ == "__main__":
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=5000,
        log_level="info"
    ) 