"""
PyInstaller钩子：收集requests + BeautifulSoup + html2text依赖
用于exe环境下的备用网页抓取方案
"""

from PyInstaller.utils.hooks import collect_all

# 收集requests相关模块
requests_data = collect_all('requests')
datas = requests_data[0]
binaries = requests_data[1] 
hiddenimports = requests_data[2]

# 收集BeautifulSoup4相关模块
bs4_data = collect_all('bs4')
datas += bs4_data[0]
binaries += bs4_data[1]
hiddenimports += bs4_data[2]

# 收集html2text相关模块
html2text_data = collect_all('html2text')
datas += html2text_data[0]
binaries += html2text_data[1]
hiddenimports += html2text_data[2]

# 添加常见的网络请求相关模块
hiddenimports += [
    'requests.adapters',
    'requests.auth', 
    'requests.cookies',
    'requests.exceptions',
    'requests.models',
    'requests.sessions',
    'requests.packages.urllib3',
    'requests.packages.urllib3.util',
    'requests.packages.urllib3.exceptions',
    'urllib3',
    'urllib3.util',
    'urllib3.exceptions',
    'urllib3.response',
    'urllib3.poolmanager',
    'certifi',
    'idna',
    'charset_normalizer',
    'bs4.builder',
    'bs4.dammit',
    'bs4.element',
    'bs4.formatter',
    'bs4.builder._htmlparser',
    'bs4.builder._lxml',
    'lxml',
    'lxml.html',
    'lxml.etree',
    'html.parser',
    'html2text.config',
    'html2text.cli'
] 