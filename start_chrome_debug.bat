@echo off
chcp 65001 >nul
echo Starting Chrome browser in debug mode...
echo ================================

REM Find Chrome installation path
set CHROME_PATH=""

REM Common Chrome installation paths
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
) else (
    echo Chrome browser not found
    echo Please install Chrome browser or check installation path
    pause
    exit /b 1
)

echo Found Chrome: %CHROME_PATH%

REM Create debug user data directory
set DEBUG_DIR=%TEMP%\chrome-debug
if not exist "%DEBUG_DIR%" mkdir "%DEBUG_DIR%"

echo Launch parameters:
echo    --remote-debugging-port=9222
echo    --user-data-dir=%DEBUG_DIR%
echo    --disable-web-security
echo    --disable-features=VizDisplayCompositor

echo.
echo Starting Chrome browser...
echo Please login to Google AI Studio in the browser
echo Visit: https://aistudio.google.com/prompts/new_chat

REM Start Chrome
start "" %CHROME_PATH% --remote-debugging-port=9222 --user-data-dir="%DEBUG_DIR%" --disable-web-security --disable-features=VizDisplayCompositor "https://aistudio.google.com/prompts/new_chat"

echo.
echo Chrome started successfully!
echo Next steps:
echo    1. Login to Google account in the browser
echo    2. Make sure you can access Google AI Studio
echo    3. Run automation script: python connect_existing_browser.py
echo.
pause
