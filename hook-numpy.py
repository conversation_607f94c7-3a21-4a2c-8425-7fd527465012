#!/usr/bin/env python3
"""
NumPy 2.3.1 专用打包钩子
确保所有必要的NumPy核心模块都被正确包含在PyInstaller打包中
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有numpy子模块
hiddenimports = collect_submodules('numpy')

# 添加NumPy 2.3.1特定的核心模块
hiddenimports.extend([
    'numpy._core._exceptions',
    'numpy._core.multiarray',
    'numpy._core.umath',
    'numpy._core.multiarray_umath',
    'numpy._core.numerictypes',
    'numpy._core.function_base',
    'numpy._core._internal',
    'numpy._core._asarray',
    'numpy._core._dtype',
    'numpy._core._machar',
    'numpy._core._methods',
    'numpy._core._ufunc_config',
    'numpy.lib.format',
    'numpy.lib.utils',
    'numpy.lib.arraysetops',
    'numpy.lib.npyio',
])

# 收集numpy的数据文件
datas = collect_data_files('numpy')

print(f"NumPy打包钩子: 包含了 {len(hiddenimports)} 个模块") 