#!/usr/bin/env python3
"""
持久化Google AI Studio自动化工具
- 保持浏览器进程不关闭
- 自动登录Google账号（从配置文件读取）
- 自动处理提示词和响应
"""

import json
import time
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class PersistentAIStudio:
    def __init__(self, config_file="config.json"):
        """初始化持久化AI Studio客户端"""
        self.config = self.load_config(config_file)
        self.driver = None
        self.is_logged_in = False
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
            return config
        except FileNotFoundError:
            print(f"❌ 配置文件未找到: {config_file}")
            print("💡 请创建配置文件并填入您的Google账号信息")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            sys.exit(1)
    
    def setup_chrome_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        
        # 使用持久化用户数据目录
        user_data_dir = os.path.abspath(self.config["browser_settings"]["user_data_dir"])
        chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
        
        # 基本选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 窗口大小
        window_size = self.config["browser_settings"]["window_size"]
        chrome_options.add_argument(f"--window-size={window_size}")
        
        # 是否无头模式
        if self.config["browser_settings"]["headless"]:
            chrome_options.add_argument("--headless")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动Chrome失败: {str(e)}")
            print("💡 请确保已安装ChromeDriver")
            return False
    
    def check_login_status(self):
        """检查登录状态"""
        try:
            current_url = self.driver.current_url
            if "accounts.google.com" in current_url:
                return False
            elif "aistudio.google.com" in current_url:
                # 检查页面是否包含登录相关元素
                try:
                    self.driver.find_element(By.CSS_SELECTOR, "textarea, [contenteditable='true']")
                    return True
                except:
                    return False
            return False
        except:
            return False
    
    def auto_login(self):
        """自动登录Google账号"""
        email = self.config["google_account"]["email"]
        password = self.config["google_account"]["password"]

        if not email or not password or email == "<EMAIL>":
            print("💡 检测到默认配置，将使用手动登录模式")
            return self.manual_login()
        
        try:
            print("🔑 开始自动登录...")
            
            # 等待邮箱输入框
            wait = WebDriverWait(self.driver, 10)
            email_input = wait.until(EC.element_to_be_clickable((By.ID, "identifierId")))
            
            # 输入邮箱
            email_input.clear()
            email_input.send_keys(email)
            print(f"📧 输入邮箱: {email}")
            
            # 点击下一步
            next_button = self.driver.find_element(By.ID, "identifierNext")
            next_button.click()
            
            # 等待密码输入框
            time.sleep(2)
            password_input = wait.until(EC.element_to_be_clickable((By.NAME, "password")))
            
            # 输入密码
            password_input.clear()
            password_input.send_keys(password)
            print("🔐 输入密码")
            
            # 点击下一步
            password_next = self.driver.find_element(By.ID, "passwordNext")
            password_next.click()
            
            # 等待登录完成
            print("⏳ 等待登录完成...")
            time.sleep(5)
            
            # 检查是否需要二次验证
            current_url = self.driver.current_url
            if "challenge" in current_url or "verify" in current_url:
                print("🔐 检测到二次验证")
                print("💡 请在浏览器中完成二次验证")
                input("完成验证后按回车继续...")
            
            return True
            
        except Exception as e:
            print(f"❌ 自动登录失败: {str(e)}")
            print("💡 请手动登录或检查账号密码")
            return False

    def manual_login(self):
        """手动登录模式"""
        print("🔑 手动登录模式")
        print("💡 请在浏览器中完成以下步骤:")
        print("   1. 输入您的Google邮箱")
        print("   2. 输入密码")
        print("   3. 完成任何二次验证")
        print("   4. 等待跳转到AI Studio页面")
        print()

        input("⏳ 请完成登录后按回车继续...")

        # 检查登录状态
        time.sleep(2)
        if self.check_login_status():
            print("✅ 登录成功！")
            return True
        else:
            print("❌ 登录失败或未完成")
            retry = input("是否重试? (y/N): ").strip().lower()
            if retry in ['y', 'yes']:
                return self.manual_login()
            return False
    
    def navigate_to_ai_studio(self):
        """导航到AI Studio"""
        url = self.config["ai_studio_settings"]["default_url"]
        print(f"🌐 访问AI Studio: {url}")
        
        self.driver.get(url)
        time.sleep(3)
        
        # 检查是否需要登录
        if not self.check_login_status():
            print("🔑 需要登录Google账号")
            if not self.auto_login():
                return False
            
            # 重新导航到AI Studio
            self.driver.get(url)
            time.sleep(3)
        
        self.is_logged_in = True
        print("✅ 成功访问AI Studio")
        return True
    
    def find_input_element(self):
        """查找输入框"""
        selectors = [
            "textarea[placeholder*='Enter a prompt']",
            "textarea[placeholder*='Type a message']",
            "textarea[data-testid='prompt-textarea']",
            "textarea[aria-label*='prompt']",
            "textarea:not([readonly]):not([disabled])",
            "div[contenteditable='true']",
            "[role='textbox']"
        ]
        
        wait = WebDriverWait(self.driver, 10)
        
        for selector in selectors:
            try:
                element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                if element.is_displayed():
                    return element
            except:
                continue
        
        # 尝试查找所有textarea
        textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
        for textarea in textareas:
            if textarea.is_displayed() and textarea.is_enabled():
                return textarea
        
        return None
    
    def find_send_button(self):
        """查找发送按钮"""
        selectors = [
            "button[aria-label*='Send']",
            "button[title*='Send']",
            "button[data-testid='send-button']",
            "button[type='submit']"
        ]
        
        wait = WebDriverWait(self.driver, 5)
        
        for selector in selectors:
            try:
                button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                if button.is_displayed():
                    return button
            except:
                continue
        
        # 通过文本查找按钮
        buttons = self.driver.find_elements(By.TAG_NAME, "button")
        for button in buttons:
            try:
                if not (button.is_displayed() and button.is_enabled()):
                    continue
                
                text_content = button.text.lower()
                aria_label = (button.get_attribute('aria-label') or '').lower()
                
                if 'send' in text_content or 'send' in aria_label:
                    return button
            except:
                continue
        
        return None
    
    def send_prompt(self, prompt):
        """发送提示词"""
        print(f"📝 发送提示词 ({len(prompt)} 字符)...")
        
        # 查找输入框
        input_element = self.find_input_element()
        if not input_element:
            print("❌ 未找到输入框")
            return False
        
        # 滚动到输入框并聚焦
        self.driver.execute_script("arguments[0].scrollIntoView(true);", input_element)
        time.sleep(0.5)
        input_element.click()
        
        # 清空并输入内容
        input_element.clear()
        time.sleep(0.5)
        input_element.send_keys(prompt)
        
        print("✅ 提示词输入完成")
        time.sleep(1)
        
        # 查找并点击发送按钮
        send_button = self.find_send_button()
        if not send_button:
            print("❌ 未找到发送按钮，尝试使用回车键发送")
            input_element.send_keys(Keys.RETURN)
        else:
            self.driver.execute_script("arguments[0].scrollIntoView(true);", send_button)
            time.sleep(0.5)
            send_button.click()
        
        print("🚀 提示词发送成功")
        return True
    
    def wait_for_response(self):
        """等待AI响应"""
        wait_time = self.config["ai_studio_settings"]["response_wait_time"]
        print(f"⏳ 等待AI响应 ({wait_time}秒)...")
        
        time.sleep(wait_time)
        
        # 尝试获取响应内容
        try:
            # 查找响应区域的常见选择器
            response_selectors = [
                "[data-testid='response-content']",
                ".response-content",
                ".message-content",
                "[role='article']",
                ".markdown-content"
            ]
            
            for selector in response_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        response_text = elements[-1].text  # 获取最后一个响应
                        if response_text.strip():
                            return response_text
                except:
                    continue
            
            print("💡 无法自动获取响应内容，请在浏览器中查看")
            return None
            
        except Exception as e:
            print(f"❌ 获取响应失败: {str(e)}")
            return None
    
    def start_persistent_session(self):
        """启动持久化会话"""
        print("🚀 启动持久化AI Studio会话...")
        print("="*60)
        
        # 启动浏览器
        if not self.setup_chrome_driver():
            return False
        
        # 导航到AI Studio
        if not self.navigate_to_ai_studio():
            return False
        
        print("✅ 持久化会话启动成功！")
        print("💡 浏览器将保持打开状态")
        print("="*60)
        
        return True
    
    def interactive_mode(self):
        """交互模式"""
        print("\n🤖 进入交互模式")
        print("💡 输入提示词，输入 'quit' 退出")
        print("-"*40)
        
        while True:
            try:
                prompt = input("\n📝 请输入提示词: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    print("👋 退出交互模式")
                    break
                
                if not prompt:
                    print("❌ 提示词不能为空")
                    continue
                
                # 发送提示词
                if self.send_prompt(prompt):
                    # 等待响应
                    response = self.wait_for_response()
                    if response:
                        print("\n🤖 AI响应:")
                        print("-"*40)
                        print(response)
                        print("-"*40)
                    else:
                        print("💡 请在浏览器中查看AI响应")
                else:
                    print("❌ 发送提示词失败")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出交互模式")
                break
            except Exception as e:
                print(f"❌ 操作失败: {str(e)}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("✅ 浏览器已关闭")
            except:
                pass


def main():
    """主函数"""
    print("🤖 持久化Google AI Studio自动化工具")
    print("="*60)
    
    # 创建AI Studio客户端
    ai_studio = PersistentAIStudio()
    
    try:
        # 启动持久化会话
        if ai_studio.start_persistent_session():
            # 进入交互模式
            ai_studio.interactive_mode()
        else:
            print("❌ 启动失败")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    
    finally:
        # 询问是否关闭浏览器
        try:
            choice = input("\n❓ 是否关闭浏览器? (y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                ai_studio.close()
            else:
                print("💡 浏览器将保持打开状态")
        except:
            pass


if __name__ == "__main__":
    main()
