#!/usr/bin/env python3
"""
测试真实的UNC Asia页面处理效果
"""

import requests
import json
import re


def test_unc_asia_page():
    """测试 https://asia.unc.edu/about/people 页面"""
    
    base_url = "http://localhost:5000"
    
    # 测试真实的UNC Asia页面
    test_data = {
        "url": "https://asia.unc.edu/about/people",
        "timeout": 60
    }
    
    print("🧪 测试真实UNC Asia页面处理")
    print(f"🎯 测试URL: {test_data['url']}")
    print(f"⏱️  超时设置: {test_data['timeout']}秒")
    print("-" * 60)
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=70  # HTTP请求超时略大于爬取超时
        )
        
        print(f"📥 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("📋 响应格式检查:")
            required_fields = ['success', 'url', 'markdown_content', 'processing_time', 'error']
            for field in required_fields:
                if field in result:
                    print(f"   ✅ {field}: {type(result[field]).__name__}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
            
            print(f"\n📊 处理结果:")
            print(f"   成功状态: {result['success']}")
            print(f"   处理时长: {result['processing_time']}秒")
            print(f"   URL: {result['url']}")
            print(f"   错误信息: {result['error'] if result['error'] else '无'}")
            
            if result['success']:
                content = result['markdown_content']
                print(f"   内容长度: {len(content)} 字符")
                
                # 检查是否包含期望的人员信息
                expected_people = [
                    "PAMELA LOTHSPEICH",
                    "KEVIN W. FOGG", 
                    "KATHRYN ULRICH",
                    "DORI BRADY",
                    "BECKY BUTLER",
                    "HELEN RICHARD",
                    "GABRIELLE NEAL"
                ]
                
                print(f"\n👥 人员信息检查:")
                people_found = 0
                for person in expected_people:
                    if person in content:
                        print(f"   ✅ 找到: {person}")
                        people_found += 1
                    else:
                        print(f"   ❌ 缺少: {person}")
                
                print(f"   📊 找到 {people_found}/{len(expected_people)} 位人员")
                
                # 检查超链接移除效果
                print(f"\n🔗 超链接检查:")
                link_patterns = [
                    (r'\[([^\]]*)\]\([^)]*\)', "Markdown链接 [text](url)"),
                    (r'<https?://[^>]+>', "角括号链接 <url>"),
                    (r'https?://\S+', "裸URL"),
                ]
                
                total_links = 0
                for pattern, desc in link_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"   ❌ {desc}: 发现 {len(matches)} 个")
                        total_links += len(matches)
                        # 显示前3个示例
                        for match in matches[:3]:
                            print(f"      - {match}")
                    else:
                        print(f"   ✅ {desc}: 已清除")
                
                if total_links == 0:
                    print("   🎉 所有超链接已成功移除!")
                
                # 检查邮箱地址是否正常（没有被误处理）
                print(f"\n📧 邮箱地址检查:")
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                emails = re.findall(email_pattern, content)
                
                expected_emails = [
                    "<EMAIL>",
                    "<EMAIL>", 
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ]
                
                print(f"   发现 {len(emails)} 个邮箱地址:")
                emails_found = 0
                for email in expected_emails:
                    if email in content:
                        print(f"   ✅ {email}")
                        emails_found += 1
                    else:
                        print(f"   ❌ 缺少: {email}")
                
                print(f"   📊 找到 {emails_found}/{len(expected_emails)} 个期望邮箱")
                
                # 检查反爬虫干扰文本
                print(f"\n🛡️ 反爬虫干扰检查:")
                interference_patterns = ["hsdfsf", "hsdfSf", "HSDFSF"]
                interference_found = False
                for pattern in interference_patterns:
                    if pattern.lower() in content.lower():
                        print(f"   ❌ 发现干扰文本: {pattern}")
                        interference_found = True
                
                if not interference_found:
                    print("   ✅ 未发现反爬虫干扰文本")
                
                # 显示内容预览
                print(f"\n📝 内容预览 (前500字符):")
                print("-" * 40)
                print(content[:500])
                if len(content) > 500:
                    print("... (内容已截断)")
                print("-" * 40)
                
                # 显示几个关键部分
                lines = content.split('\n')
                print(f"\n📋 关键部分预览:")
                for i, line in enumerate(lines[:20]):
                    if line.strip() and any(name in line for name in expected_people[:3]):
                        print(f"   {i+1:2d}: {line[:80]}...")
                
                return True
                
            else:
                # 处理失败的情况
                print(f"\n❌ 页面处理失败")
                print(f"   错误信息: {result['error']}")
                print(f"   这可能是网络问题或者页面访问限制")
                
                # 检查是否是超时
                if "超时" in result['error'] or "timeout" in result['error'].lower():
                    print("   🕐 看起来是超时问题，可以尝试增加timeout值")
                
                return False
        
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ HTTP请求超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


def analyze_expected_content():
    """分析期望的页面内容"""
    
    print("\n📋 页面内容分析 (基于实际内容):")
    print("🎯 该页面包含以下主要信息:")
    
    expected_content = {
        "人员列表": [
            "DR. PAMELA LOTHSPEICH - Director",
            "DR. KEVIN W. FOGG - Associate Director", 
            "KATHRYN ULRICH - Business Manager",
            "DORI BRADY - Program Director",
            "DR. BECKY BUTLER - Assistant Director",
            "HELEN RICHARD - Program Associate",
            "GABRIELLE NEAL - Visiting Scholars Assistant"
        ],
        "邮箱地址": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ],
        "联系信息": [
            "919.843.0130",
            "919.843.9065", 
            "919.843.0129",
            "919.843.7522",
            "919.445.1271",
            "919.843.9203"
        ],
        "地址": "FedEx Global Education Center, Chapel Hill, NC 27599"
    }
    
    for category, items in expected_content.items():
        print(f"\n   {category}:")
        for item in items:
            print(f"     - {item}")
    
    print(f"\n💡 测试重点:")
    print("   ✅ 确保所有人员信息都被正确提取")
    print("   ✅ 确保邮箱地址格式正确（无干扰文本）") 
    print("   ✅ 确保超链接被完全移除")
    print("   ✅ 确保响应格式符合API规范")


if __name__ == "__main__":
    print("🚀 UNC Asia页面测试")
    print("🎯 测试真实页面的处理效果")
    print("=" * 70)
    
    # 分析期望内容
    analyze_expected_content()
    
    # 执行测试
    success = test_unc_asia_page()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 UNC Asia页面处理测试通过!")
        print("✅ 页面内容成功提取并清理")
    else:
        print("⚠️  UNC Asia页面处理有问题")
        print("💡 可能的原因:")
        print("   - 网络连接问题")
        print("   - 页面访问限制") 
        print("   - 超时设置过短")
        print("   - 服务配置问题")
    
    print(f"\n🔧 如果失败，可以尝试:")
    print("   1. 检查服务是否正在运行: python web_service.py")
    print("   2. 增加超时时间")
    print("   3. 检查网络连接") 