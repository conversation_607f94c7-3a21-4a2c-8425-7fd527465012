#!/usr/bin/env python3
"""
Crawl4AI 依赖诊断脚本
用于检查crawl4ai初始化失败的原因
"""

import sys
import os
import traceback

def check_imports():
    """检查关键模块的导入情况"""
    print("🔍 检查模块导入...")
    
    modules_to_check = [
        ('numpy', 'NumPy数值计算库'),
        ('playwright', 'Playwright浏览器自动化'),
        ('crawl4ai', 'Crawl4AI主模块'),
        ('fake_useragent', 'User Agent伪造'),
        ('fake_http_header', 'HTTP头伪造'),
        ('playwright_stealth', 'Playwright反检测'),
        ('aiohttp', '异步HTTP客户端'),
        ('bs4', 'BeautifulSoup HTML解析'),
    ]
    
    results = {}
    for module_name, description in modules_to_check:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', '未知版本')
            print(f"  ✅ {module_name} ({description}): {version}")
            results[module_name] = True
        except ImportError as e:
            print(f"  ❌ {module_name} ({description}): 导入失败 - {e}")
            results[module_name] = False
        except Exception as e:
            print(f"  ⚠️ {module_name} ({description}): 导入异常 - {e}")
            results[module_name] = False
    
    return results

def check_crawl4ai_init():
    """检查Crawl4AI的初始化"""
    print("\n🚀 检查Crawl4AI初始化...")
    
    try:
        from crawl4ai import AsyncWebCrawler
        print("  ✅ AsyncWebCrawler 导入成功")
        
        # 尝试创建实例
        crawler = AsyncWebCrawler()
        print("  ✅ AsyncWebCrawler 实例创建成功")
        return True
        
    except Exception as e:
        print(f"  ❌ Crawl4AI初始化失败: {e}")
        print("  详细错误信息:")
        traceback.print_exc()
        return False

def check_playwright_setup():
    """检查Playwright浏览器设置"""
    print("\n🌐 检查Playwright设置...")
    
    try:
        import playwright
        from playwright.async_api import async_playwright
        print("  ✅ Playwright导入成功")
        
        # 检查浏览器是否安装
        import asyncio
        
        async def check_browser():
            try:
                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)
                    print("  ✅ Chromium浏览器启动成功")
                    await browser.close()
                    return True
            except Exception as e:
                print(f"  ❌ 浏览器启动失败: {e}")
                return False
        
        result = asyncio.run(check_browser())
        return result
        
    except Exception as e:
        print(f"  ❌ Playwright检查失败: {e}")
        return False

def check_environment():
    """检查环境配置"""
    print("\n🔧 检查环境配置...")
    
    important_vars = [
        'OMP_NUM_THREADS',
        'OPENBLAS_NUM_THREADS', 
        'MKL_NUM_THREADS',
        'NUMPY_MADVISE_HUGEPAGE'
    ]
    
    for var in important_vars:
        value = os.environ.get(var, '未设置')
        print(f"  {var}: {value}")

def main():
    print("=" * 50)
    print("Crawl4AI 依赖诊断工具")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查导入
    import_results = check_imports()
    
    # 检查环境
    check_environment()
    
    # 检查Crawl4AI
    crawl4ai_ok = check_crawl4ai_init()
    
    # 检查Playwright
    playwright_ok = check_playwright_setup()
    
    print("\n" + "=" * 50)
    print("诊断结果总结:")
    print("=" * 50)
    
    critical_modules = ['numpy', 'playwright', 'crawl4ai']
    all_critical_ok = all(import_results.get(mod, False) for mod in critical_modules)
    
    if all_critical_ok and crawl4ai_ok and playwright_ok:
        print("✅ 所有检查通过！Crawl4AI应该可以正常工作。")
    else:
        print("❌ 发现问题，需要解决：")
        if not all_critical_ok:
            print("  - 关键模块导入失败")
        if not crawl4ai_ok:
            print("  - Crawl4AI初始化失败")
        if not playwright_ok:
            print("  - Playwright浏览器设置有问题")

if __name__ == "__main__":
    main() 