import asyncio
import time
import re
from typing import Dict, Any
from pydantic import BaseModel, HttpUrl
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
import uvicorn

# 导入crawl4ai相关模块
from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import CrawlerRunConfig


def remove_markdown_links(markdown_text: str) -> str:
    """
    移除markdown文本中的所有超链接和反爬虫干扰文本
    
    Args:
        markdown_text: 原始markdown文本
        
    Returns:
        移除超链接和干扰文本后的markdown文本
    """
    if not markdown_text:
        return markdown_text
    
    # 移除常见的反爬虫干扰文本
    # 移除 "hsdfsf" 等无意义的重复字符串
    markdown_text = re.sub(r'hsdfsf', '', markdown_text, flags=re.IGNORECASE)
    
    # 移除其他常见的干扰模式
    # 移除重复的无意义字符组合
    markdown_text = re.sub(r'(?i)[a-z]{5,}(?=[a-z]*@)', lambda m: '' if len(set(m.group())) <= 2 else m.group(), markdown_text)
    
    # 修复被干扰的邮箱地址格式
    # 将类似 "<EMAIL>" 中间被插入干扰文本的情况修复
    markdown_text = re.sub(r'(\w+)@(\w+)\.(\w+)', r'\1@\2.\3', markdown_text)
    
    # 移除图片链接 ![alt](url) -> alt (保留描述文字，移除感叹号)
    markdown_text = re.sub(r'!\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除普通链接 [text](url) -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用链接 [text][ref] -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除引用定义 [ref]: url
    markdown_text = re.sub(r'^\s*\[([^\]]+)\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除直接URL链接 <url>
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除自动链接（裸URL）
    markdown_text = re.sub(r'https?://\S+', '', markdown_text)
    
    # 清理多余的空行
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    
    # 清理多余的空格（但保留双空格分隔符）
    markdown_text = re.sub(r'[ ]{3,}', '  ', markdown_text)
    
    return markdown_text.strip()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    url: HttpUrl
    timeout: int = 60


class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    url: str
    markdown_content: str
    processing_time: float
    error: str = ""  # 错误信息字段


# 创建FastAPI应用 - 禁用默认的 docs_url 和 redoc_url
app = FastAPI(
    title="Crawl4AI Web Service",
    description="基于Crawl4AI的网页内容抓取服务，自动移除超链接和反爬虫干扰文本",
    version="1.0.0",
    docs_url=None,  # 禁用默认的 docs
    redoc_url=None  # 禁用默认的 redoc
)

# 自定义 Swagger UI 页面，使用 CDNJS（经测试可用）
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        # 使用 CDNJS（经过测试，状态良好）
        swagger_js_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui.css",
        # 添加 favicon
        swagger_favicon_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/favicon-32x32.png",
        # 自定义配置
        init_oauth={
            "usePkceWithAuthorizationCodeGrant": True
        },
    )

# 自定义 ReDoc 页面，使用 CDNJS
@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        # 使用 CDNJS
        redoc_js_url="https://cdnjs.cloudflare.com/ajax/libs/redoc/2.1.5/bundles/redoc.standalone.js",
        # 自定义主题
        redoc_favicon_url="https://cdnjs.cloudflare.com/ajax/libs/redoc/2.1.5/docs/images/redoc-logo.png",
    )

# 添加 CDN 状态检查接口
@app.get("/cdn-status")
async def check_cdn_status():
    """检查各个 CDN 的连接状态"""
    import aiohttp
    import asyncio
    
    cdn_urls = {
        "unpkg": "https://unpkg.com/swagger-ui-dist@5/swagger-ui.css",
        "jsdelivr": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        "cdnjs": "https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui.css"
    }
    
    status = {}
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
        for name, url in cdn_urls.items():
            try:
                async with session.head(url) as response:
                    status[name] = {
                        "url": url,
                        "status": response.status,
                        "accessible": response.status == 200,
                        "response_time": f"{response.headers.get('X-Response-Time', 'N/A')}"
                    }
            except Exception as e:
                status[name] = {
                    "url": url,
                    "status": "error",
                    "accessible": False,
                    "error": str(e)
                }
    
    return {
        "current_cdn": "unpkg",
        "alternatives": status,
        "recommendation": "如果当前 CDN 无法访问，请尝试重启服务或更换网络环境"
    }


@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "message": "Crawl4AI Web Service",
        "version": "1.0.0",
        "description": "网页内容抓取服务，自动移除markdown中的超链接和反爬虫干扰文本",
        "endpoints": {
            "/markdown": "POST - 网页内容抓取并转换为Markdown格式（无超链接，无干扰文本）"
        },
        "response_format": {
            "success": "bool - 是否成功",
            "url": "string - 请求的URL",
            "markdown_content": "string - 清理后的Markdown内容（成功时有内容，失败时为空）",
            "processing_time": "float - 处理耗时（秒）",
            "error": "string - 错误信息（成功时为空，失败时包含具体错误）"
        }
    }


@app.post("/markdown", response_model=CrawlResponse)
async def crawl_to_markdown(request: CrawlRequest):
    """
    爬取网页内容并转换为Markdown格式（移除超链接）
    
    Args:
        request: 包含url和timeout的请求参数
        
    Returns:
        CrawlResponse: 包含成功状态、URL、Markdown内容（无超链接）和处理时长
    """
    start_time = time.time()
    
    try:
        # 使用AsyncWebCrawler进行网页爬取
        async with AsyncWebCrawler() as crawler:
            # 执行爬取，设置超时时间
            timeout_ms = request.timeout * 1000  # 转换为毫秒
            config = CrawlerRunConfig(
                page_timeout=timeout_ms,
                wait_until="domcontentloaded",
                verbose=False  # 减少日志输出
            )
            
            # 添加调试信息（可选，生产环境可以移除）
            #print(f"设置超时: {request.timeout}秒 ({timeout_ms}毫秒)")
            
            result = await crawler.arun(
                url=str(request.url),
                config=config
            )
            
            # 计算处理时间
            processing_time = round(time.time() - start_time, 3)
            
            # 检查爬取是否成功
            if result.success:
                # 获取原始markdown内容
                raw_markdown = ""
                if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                    raw_markdown = result.markdown.raw_markdown or ""
                
                # 移除超链接
                clean_markdown = remove_markdown_links(raw_markdown)
                
                return CrawlResponse(
                    success=True,
                    url=str(request.url),
                    markdown_content=clean_markdown,
                    processing_time=processing_time,
                    error=""  # 成功时error为空
                )
            else:
                # 爬取失败
                error_msg = getattr(result, 'error_message', '未知错误')
                
                # 检查是否是超时错误并转换为简洁格式
                if error_msg and (("Timeout" in error_msg and "exceeded" in error_msg) or "timeout" in error_msg.lower()):
                    # 提取超时时间
                    timeout_info = f"{request.timeout}秒"
                    if "Timeout" in error_msg and "ms exceeded" in error_msg:
                        timeout_match = re.search(r'Timeout (\d+)ms exceeded', error_msg)
                        if timeout_match:
                            actual_timeout_ms = int(timeout_match.group(1))
                            actual_timeout_sec = actual_timeout_ms / 1000
                            timeout_info = f"{actual_timeout_sec}秒"
                    
                    error_msg = f"抓取超时：{timeout_info}"
                # 检查其他常见错误类型
                elif error_msg and ("connection" in error_msg.lower() or "network" in error_msg.lower()):
                    error_msg = "网络连接失败"
                elif error_msg and ("403" in error_msg or "forbidden" in error_msg.lower()):
                    error_msg = "访问被拒绝"
                elif error_msg and len(error_msg) > 200:  # 如果错误信息太长，简化它
                    error_msg = "网页抓取失败"
                
                return CrawlResponse(
                    success=False,
                    url=str(request.url),
                    markdown_content="",  # 失败时markdown_content为空
                    processing_time=processing_time,
                    error=error_msg
                )
                
    except asyncio.TimeoutError:
        processing_time = round(time.time() - start_time, 3)
        return CrawlResponse(
            success=False,
            url=str(request.url),
            markdown_content="",  # 超时时markdown_content为空
            processing_time=processing_time,
            error=f"抓取超时：{request.timeout}秒"
        )
        
    except Exception as e:
        processing_time = round(time.time() - start_time, 3)
        
        # 检查是否是超时相关的错误
        error_str = str(e)
        if ("Timeout" in error_str and "exceeded" in error_str) or "timeout" in error_str.lower():
            # 提取超时时间（如果能找到的话）
            timeout_info = f"{request.timeout}秒"
            if "Timeout" in error_str and "ms exceeded" in error_str:
                # 从错误信息中提取实际超时时间
                timeout_match = re.search(r'Timeout (\d+)ms exceeded', error_str)
                if timeout_match:
                    actual_timeout_ms = int(timeout_match.group(1))
                    actual_timeout_sec = actual_timeout_ms / 1000
                    timeout_info = f"{actual_timeout_sec}秒"
            
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=f"抓取超时：{timeout_info}"
            )
        
        # 检查是否是网络连接错误
        elif ("connection" in error_str.lower() or 
              "network" in error_str.lower() or 
              "dns" in error_str.lower() or
              "resolve" in error_str.lower()):
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error="网络连接失败，无法访问目标网站。请检查：1) 网络连接是否正常；2) 目标网站地址是否正确；3) 目标网站是否可正常访问"
            )
        
        # 检查是否是权限或访问被拒绝的错误
        elif ("403" in error_str or "forbidden" in error_str.lower() or 
              "access denied" in error_str.lower() or "blocked" in error_str.lower()):
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error="访问被拒绝，目标网站可能限制了爬虫访问。建议：1) 检查网站的访问政策；2) 尝试更换User-Agent；3) 确认是否需要登录或特殊权限"
            )
        
        # 其他未知错误，提供简化的错误信息
        else:
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=f"处理过程中发生错误，无法完成网页内容抓取。如果问题持续，请检查目标网站是否可正常访问"
            )


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "crawl4ai-web-service"}


if __name__ == "__main__":
    print("🚀 启动 Crawl4AI Web Service...")
    print("📡 服务将在 http://localhost:5000 运行")
    print("📚 API文档可在 http://localhost:5000/docs 查看")
    print("🔍 /markdown 接口用于网页内容抓取")
    print("🔗 自动移除markdown内容中的超链接")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=5000,
        log_level="info"
    ) 