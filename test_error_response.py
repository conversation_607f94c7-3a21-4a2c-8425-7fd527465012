#!/usr/bin/env python3
"""
测试错误响应格式是否正确
"""

import requests
import json


def test_timeout_response():
    """测试超时响应格式"""
    
    base_url = "http://localhost:5000"
    
    # 测试一个可能超时的URL
    test_data = {
        "url": "https://asia.unc.edu/about/people",  # 这个URL之前超时了
        "timeout": 30  # 设置较短的超时时间
    }
    
    print("🧪 测试超时/错误响应格式")
    print(f"🎯 测试URL: {test_data['url']}")
    print(f"⏱️  超时设置: {test_data['timeout']}秒")
    print("-" * 50)
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=35  # HTTP请求超时略大于爬取超时
        )
        
        print(f"📥 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("📋 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            print("\n🔍 格式检查:")
            
            # 检查必需字段
            required_fields = ['success', 'url', 'markdown_content', 'processing_time', 'error']
            all_present = True
            for field in required_fields:
                if field in result:
                    print(f"   ✅ {field}: {type(result[field]).__name__}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
                    all_present = False
            
            if not all_present:
                return False
            
            # 检查失败响应的正确性
            if not result['success']:
                print("\n📝 失败响应验证:")
                
                # markdown_content应该为空
                if result['markdown_content'] == "":
                    print("   ✅ markdown_content为空（正确）")
                else:
                    print(f"   ❌ markdown_content不为空: '{result['markdown_content'][:50]}...'")
                
                # error应该包含信息
                if result['error']:
                    print(f"   ✅ error包含错误信息: {result['error'][:100]}...")
                else:
                    print("   ❌ error字段为空")
                
                # processing_time应该大于0
                if result['processing_time'] > 0:
                    print(f"   ✅ processing_time正常: {result['processing_time']}秒")
                else:
                    print(f"   ❌ processing_time异常: {result['processing_time']}")
                
                # url应该匹配
                if result['url'] == test_data['url']:
                    print("   ✅ url匹配")
                else:
                    print(f"   ❌ url不匹配: 期望{test_data['url']}, 实际{result['url']}")
                
                return (result['markdown_content'] == "" and 
                       bool(result['error']) and 
                       result['processing_time'] > 0 and 
                       result['url'] == test_data['url'])
            
            else:
                print("\n📝 意外：请求成功了（可能网络状况改善）")
                
                # 成功响应验证
                if result['error'] == "":
                    print("   ✅ error为空（正确）")
                else:
                    print(f"   ❌ 成功响应但error不为空: {result['error']}")
                
                if result['markdown_content']:
                    print("   ✅ markdown_content有内容")
                else:
                    print("   ❌ 成功响应但markdown_content为空")
                
                return result['error'] == "" and bool(result['markdown_content'])
        
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ HTTP请求本身超时（这不是我们要测试的超时）")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


def test_invalid_url_response():
    """测试无效URL的错误响应"""
    
    base_url = "http://localhost:5000"
    
    # 测试一个无效的URL
    test_data = {
        "url": "https://this-domain-absolutely-does-not-exist-12345.com",
        "timeout": 10
    }
    
    print("\n🧪 测试无效URL错误响应格式")
    print(f"🎯 测试URL: {test_data['url']}")
    print("-" * 50)
    
    try:
        response = requests.post(
            f"{base_url}/markdown",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("📋 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 应该是失败响应
            if not result['success']:
                success_check = (result['markdown_content'] == "" and 
                               bool(result['error']) and 
                               result['processing_time'] > 0)
                print(f"\n✅ 无效URL错误响应格式{'正确' if success_check else '有问题'}")
                return success_check
            else:
                print("\n❌ 意外：无效URL居然返回成功")
                return False
        
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 错误响应格式测试")
    print("🎯 验证失败时的JSON格式是否符合要求")
    print("=" * 60)
    
    print("\n📌 正确的失败响应格式应该是:")
    print("""
{
  "success": false,
  "url": "...", 
  "markdown_content": "",  // 失败时为空
  "processing_time": x.xxx,
  "error": "具体错误信息"  // 失败时包含错误
}
    """)
    
    # 测试超时响应
    timeout_ok = test_timeout_response()
    
    # 测试无效URL响应  
    invalid_ok = test_invalid_url_response()
    
    print("\n" + "=" * 60)
    if timeout_ok and invalid_ok:
        print("🎉 所有错误响应格式测试通过！")
        print("✅ 失败时正确返回：success=false, markdown_content='', error='具体信息'")
    else:
        print("⚠️  部分错误响应格式测试失败")
        if not timeout_ok:
            print("❌ 超时响应格式有问题")
        if not invalid_ok:
            print("❌ 无效URL响应格式有问题")
    
    print("\n💡 说明：根据您的要求，失败时应该：")
    print("- success: false")
    print("- markdown_content: '' (空字符串)")
    print("- error: '具体错误信息' (非空)")
    print("- processing_time: 实际耗时") 