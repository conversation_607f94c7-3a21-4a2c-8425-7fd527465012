#!/usr/bin/env python3
"""
快速Google AI Studio自动化脚本
简化版本，专注于核心功能
"""

import asyncio
import sys
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig


async def send_to_ai_studio(prompt: str, headless: bool = False):
    """
    发送提示词到Google AI Studio
    
    Args:
        prompt: 提示词文本
        headless: 是否无头模式
    """
    print(f"🚀 启动浏览器 (无头模式: {headless})")
    
    browser_config = BrowserConfig(
        browser_type="chromium",
        headless=headless,
        viewport_width=1920,
        viewport_height=1080
    )
    
    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            print("🌐 访问 Google AI Studio...")
            
            # 访问页面
            result = await crawler.arun(
                url="https://aistudio.google.com/prompts/new_chat",
                config=CrawlerRunConfig(
                    session_id="ai_session",
                    delay_before_return_html=5000,  # 等待5秒加载
                    page_timeout=60000
                )
            )
            
            if not result.success:
                print(f"❌ 页面访问失败: {result.error_message}")
                return False
            
            print("✅ 页面加载成功")
            
            # 填充提示词并发送
            automation_js = f"""
            console.log('开始自动化操作...');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 查找输入框
            const inputSelectors = [
                'textarea[placeholder*="Enter a prompt"]',
                'textarea[placeholder*="Type a message"]', 
                'textarea[data-testid="prompt-textarea"]',
                'textarea:not([readonly])',
                'div[contenteditable="true"]',
                '[role="textbox"]'
            ];
            
            let inputElement = null;
            for (const selector of inputSelectors) {{
                inputElement = document.querySelector(selector);
                if (inputElement) {{
                    console.log('找到输入框:', selector);
                    break;
                }}
            }}
            
            if (!inputElement) {{
                console.error('未找到输入框');
                return {{ success: false, error: '未找到输入框' }};
            }}
            
            // 填充文本
            const promptText = `{prompt.replace('`', '\\`').replace('${', '\\${')}`;
            
            if (inputElement.tagName.toLowerCase() === 'textarea') {{
                inputElement.value = promptText;
                inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                inputElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
            }} else {{
                inputElement.textContent = promptText;
                inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
            }}
            
            inputElement.focus();
            console.log('文本已填充');
            
            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找发送按钮
            const buttonSelectors = [
                'button[aria-label*="Send"]',
                'button[title*="Send"]', 
                'button[data-testid="send-button"]',
                'button[type="submit"]'
            ];
            
            let sendButton = null;
            for (const selector of buttonSelectors) {{
                sendButton = document.querySelector(selector);
                if (sendButton && !sendButton.disabled) {{
                    console.log('找到发送按钮:', selector);
                    break;
                }}
            }}
            
            // 如果没找到，通过文本查找
            if (!sendButton) {{
                const buttons = document.querySelectorAll('button');
                for (const btn of buttons) {{
                    const text = btn.textContent?.toLowerCase() || '';
                    if ((text.includes('send') || btn.querySelector('svg')) && !btn.disabled) {{
                        sendButton = btn;
                        console.log('通过文本找到发送按钮');
                        break;
                    }}
                }}
            }}
            
            if (!sendButton) {{
                console.error('未找到发送按钮');
                return {{ success: false, error: '未找到发送按钮' }};
            }}
            
            // 点击发送
            sendButton.click();
            console.log('已点击发送按钮');
            
            return {{ success: true, message: '提示词发送成功' }};
            """
            
            print("📝 填充提示词并发送...")
            
            send_result = await crawler.arun(
                url="https://aistudio.google.com/prompts/new_chat",
                config=CrawlerRunConfig(
                    session_id="ai_session",
                    js_code=[automation_js],
                    js_only=True,
                    delay_before_return_html=3000
                )
            )
            
            if send_result.success:
                print("✅ 提示词发送成功！")
                print("💡 请在浏览器中查看AI的响应")
                
                # 如果是无头模式，给用户一些时间查看结果
                if not headless:
                    print("🔍 浏览器将保持打开状态，您可以查看响应...")
                    print("⏳ 等待30秒后自动关闭，或按Ctrl+C提前退出")
                    try:
                        await asyncio.sleep(30)
                    except KeyboardInterrupt:
                        print("👋 用户中断，正在关闭...")
                
                return True
            else:
                print("❌ 发送失败")
                return False
                
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        return False


def main():
    """主函数"""
    # 默认提示词
    default_prompt = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    # 检查命令行参数
    headless = "--headless" in sys.argv
    custom_prompt = None
    
    # 如果提供了自定义提示词文件
    if len(sys.argv) > 1 and not sys.argv[1].startswith("--"):
        try:
            with open(sys.argv[1], 'r', encoding='utf-8') as f:
                custom_prompt = f.read().strip()
            print(f"📄 从文件加载提示词: {sys.argv[1]}")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {sys.argv[1]}")
            return
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return
    
    prompt = custom_prompt or default_prompt
    
    print("🤖 Google AI Studio 自动化工具")
    print("="*50)
    print(f"📝 提示词长度: {len(prompt)} 字符")
    print(f"🖥️  无头模式: {headless}")
    print("="*50)
    
    # 运行自动化
    success = asyncio.run(send_to_ai_studio(prompt, headless))
    
    if success:
        print("\n✅ 任务完成！")
    else:
        print("\n❌ 任务失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
