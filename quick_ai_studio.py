#!/usr/bin/env python3
"""
快速Google AI Studio自动化脚本
简化版本，专注于核心功能
"""

import asyncio
import sys
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig


async def wait_for_response(crawler, headless=False, max_wait_time=120):
    """
    等待AI响应并尝试获取响应内容

    Args:
        crawler: AsyncWebCrawler实例
        headless: 是否无头模式
        max_wait_time: 最大等待时间（秒）

    Returns:
        str: AI的响应内容，如果获取失败返回None
    """
    print(f"⏳ 等待AI响应（最多{max_wait_time}秒）...")

    # 等待响应的JavaScript代码
    wait_js = """
    console.log('开始等待AI响应...');

    // 等待响应出现的函数
    async function waitForResponse(maxWaitTime = 120000) {
        const startTime = Date.now();
        let lastResponseLength = 0;
        let stableCount = 0;

        while (Date.now() - startTime < maxWaitTime) {
            // 查找响应容器的多种选择器
            const responseSelectors = [
                '[data-testid="response-container"]',
                '[data-testid="message-content"]',
                '.response-content',
                '.message-content',
                '[role="article"]',
                '.markdown-content',
                'div[data-message-id]'
            ];

            let responseElement = null;
            for (const selector of responseSelectors) {
                responseElement = document.querySelector(selector);
                if (responseElement) {
                    console.log('找到响应容器:', selector);
                    break;
                }
            }

            // 如果没找到特定容器，查找包含文本的div
            if (!responseElement) {
                const allDivs = document.querySelectorAll('div');
                for (const div of allDivs) {
                    const text = div.textContent || '';
                    if (text.length > 100 && text.includes('Harvard') && !div.querySelector('textarea')) {
                        responseElement = div;
                        console.log('通过内容找到响应元素');
                        break;
                    }
                }
            }

            if (responseElement) {
                const currentText = responseElement.textContent || responseElement.innerText || '';
                const currentLength = currentText.length;

                console.log('当前响应长度:', currentLength);

                // 如果响应长度没有变化，说明可能已经完成
                if (currentLength > 0 && currentLength === lastResponseLength) {
                    stableCount++;
                    if (stableCount >= 5) { // 连续5次检查都没有变化
                        console.log('响应似乎已完成');
                        return currentText;
                    }
                } else {
                    stableCount = 0;
                    lastResponseLength = currentLength;
                }

                // 如果响应很长，可能已经完成
                if (currentLength > 1000) {
                    console.log('响应内容较长，可能已完成');
                    return currentText;
                }
            }

            // 等待2秒后再次检查
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log('等待超时');
        return null;
    }

    const response = await waitForResponse();
    return { success: true, response: response };
    """

    try:
        # 执行等待响应的JavaScript
        result = await crawler.arun(
            url="https://aistudio.google.com/prompts/new_chat",
            config=CrawlerRunConfig(
                session_id="ai_session",
                js_code=[wait_js],
                js_only=True,
                delay_before_return_html=max_wait_time * 1000,
                page_timeout=(max_wait_time + 30) * 1000
            )
        )

        if result.success and result.extracted_content:
            # 尝试解析JavaScript返回的结果
            import json
            try:
                js_result = json.loads(result.extracted_content)
                if js_result.get('success') and js_result.get('response'):
                    return js_result['response']
            except:
                pass

        return None

    except Exception as e:
        print(f"⚠️ 获取响应时出错: {str(e)}")
        return None


async def send_to_ai_studio(prompt: str, headless: bool = False):
    """
    发送提示词到Google AI Studio
    
    Args:
        prompt: 提示词文本
        headless: 是否无头模式
    """
    print(f"🚀 启动浏览器 (无头模式: {headless})")
    
    browser_config = BrowserConfig(
        browser_type="chromium",
        headless=headless,
        viewport_width=1920,
        viewport_height=1080
    )
    
    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            print("🌐 访问 Google AI Studio...")
            
            # 访问页面
            result = await crawler.arun(
                url="https://aistudio.google.com/prompts/new_chat",
                config=CrawlerRunConfig(
                    session_id="ai_session",
                    delay_before_return_html=8000,  # 等待8秒加载
                    page_timeout=120000  # 增加到120秒超时
                )
            )
            
            if not result.success:
                print(f"❌ 页面访问失败: {result.error_message}")
                return False
            
            print("✅ 页面加载成功")
            
            # 填充提示词并发送
            automation_js = f"""
            console.log('开始自动化操作...');

            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 5000));

            // 查找输入框 - 更全面的选择器
            const inputSelectors = [
                'textarea[placeholder*="Enter a prompt"]',
                'textarea[placeholder*="Type a message"]',
                'textarea[data-testid="prompt-textarea"]',
                'textarea[aria-label*="prompt"]',
                'textarea:not([readonly]):not([disabled])',
                'div[contenteditable="true"]',
                '[role="textbox"]',
                '.input-textarea',
                '[data-testid="input-textarea"]'
            ];

            let inputElement = null;
            for (const selector of inputSelectors) {{
                try {{
                    inputElement = document.querySelector(selector);
                    if (inputElement && inputElement.offsetParent !== null) {{ // 确保元素可见
                        console.log('找到输入框:', selector);
                        break;
                    }}
                }} catch (e) {{
                    console.log('选择器错误:', selector, e);
                }}
            }}

            // 如果还没找到，尝试所有textarea
            if (!inputElement) {{
                const allTextareas = document.querySelectorAll('textarea');
                console.log('找到textarea总数:', allTextareas.length);
                for (const textarea of allTextareas) {{
                    if (textarea.offsetParent !== null && !textarea.readOnly && !textarea.disabled) {{
                        inputElement = textarea;
                        console.log('使用可见的textarea');
                        break;
                    }}
                }}
            }}

            if (!inputElement) {{
                console.error('未找到输入框');
                return {{ success: false, error: '未找到输入框' }};
            }}

            // 滚动到输入框并聚焦
            inputElement.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
            await new Promise(resolve => setTimeout(resolve, 1000));
            inputElement.focus();

            // 清空现有内容
            if (inputElement.tagName.toLowerCase() === 'textarea') {{
                inputElement.value = '';
            }} else {{
                inputElement.textContent = '';
            }}

            // 填充文本
            const promptText = `{prompt.replace('`', '\\`').replace('${', '\\${')}`;
            console.log('准备填充文本，长度:', promptText.length);

            if (inputElement.tagName.toLowerCase() === 'textarea') {{
                // 模拟真实输入
                inputElement.value = promptText;
                inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                inputElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
                inputElement.dispatchEvent(new KeyboardEvent('keyup', {{ bubbles: true }}));
            }} else {{
                inputElement.textContent = promptText;
                inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
            }}

            console.log('文本已填充');

            // 等待输入生效
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 查找发送按钮 - 更全面的选择器
            const buttonSelectors = [
                'button[aria-label*="Send"]',
                'button[title*="Send"]',
                'button[data-testid="send-button"]',
                'button[type="submit"]',
                'button[aria-label*="发送"]',
                '[role="button"][aria-label*="Send"]'
            ];

            let sendButton = null;
            for (const selector of buttonSelectors) {{
                try {{
                    sendButton = document.querySelector(selector);
                    if (sendButton && !sendButton.disabled && sendButton.offsetParent !== null) {{
                        console.log('找到发送按钮:', selector);
                        break;
                    }}
                }} catch (e) {{
                    console.log('按钮选择器错误:', selector, e);
                }}
            }}

            // 如果没找到，通过更详细的方式查找
            if (!sendButton) {{
                const buttons = document.querySelectorAll('button');
                console.log('找到按钮总数:', buttons.length);
                for (const btn of buttons) {{
                    if (btn.disabled || btn.offsetParent === null) continue;

                    const text = btn.textContent?.toLowerCase() || '';
                    const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
                    const title = (btn.getAttribute('title') || '').toLowerCase();
                    const hasSvg = btn.querySelector('svg') !== null;

                    if (text.includes('send') || ariaLabel.includes('send') || title.includes('send') ||
                        text.includes('发送') || ariaLabel.includes('发送') ||
                        (hasSvg && (ariaLabel.includes('send') || title.includes('send')))) {{
                        sendButton = btn;
                        console.log('通过内容找到发送按钮:', text, ariaLabel, title);
                        break;
                    }}
                }}
            }}

            if (!sendButton) {{
                console.error('未找到发送按钮');
                return {{ success: false, error: '未找到发送按钮' }};
            }}

            // 滚动到按钮并点击
            sendButton.scrollIntoView({{ behavior: 'smooth', block: 'center' }});
            await new Promise(resolve => setTimeout(resolve, 500));

            console.log('准备点击发送按钮...');
            sendButton.click();
            console.log('已点击发送按钮');

            // 等待响应开始
            await new Promise(resolve => setTimeout(resolve, 3000));

            return {{ success: true, message: '提示词发送成功' }};
            """
            
            print("📝 填充提示词并发送...")
            
            send_result = await crawler.arun(
                url="https://aistudio.google.com/prompts/new_chat",
                config=CrawlerRunConfig(
                    session_id="ai_session",
                    js_code=[automation_js],
                    js_only=True,
                    delay_before_return_html=3000
                )
            )
            
            if send_result.success:
                print("✅ 提示词发送成功！")
                print("⏳ 等待AI响应...")

                # 等待并尝试获取响应
                response_text = await wait_for_response(crawler, headless)

                if response_text:
                    print("🎉 获取到AI响应！")
                    print("="*50)
                    print(response_text)
                    print("="*50)
                else:
                    print("💡 请在浏览器中查看AI的响应")

                # 如果是可视模式，给用户一些时间查看结果
                if not headless:
                    print("🔍 浏览器将保持打开状态，您可以查看完整响应...")
                    print("⏳ 等待60秒后自动关闭，或按Ctrl+C提前退出")
                    try:
                        await asyncio.sleep(60)
                    except KeyboardInterrupt:
                        print("👋 用户中断，正在关闭...")

                return True
            else:
                print("❌ 发送失败")
                return False
                
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        return False


def main():
    """主函数"""
    # 默认提示词
    default_prompt = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    # 检查命令行参数
    headless = "--headless" in sys.argv
    custom_prompt = None
    
    # 如果提供了自定义提示词文件
    if len(sys.argv) > 1 and not sys.argv[1].startswith("--"):
        try:
            with open(sys.argv[1], 'r', encoding='utf-8') as f:
                custom_prompt = f.read().strip()
            print(f"📄 从文件加载提示词: {sys.argv[1]}")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {sys.argv[1]}")
            return
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return
    
    prompt = custom_prompt or default_prompt
    
    print("🤖 Google AI Studio 自动化工具")
    print("="*50)
    print(f"📝 提示词长度: {len(prompt)} 字符")
    print(f"🖥️  无头模式: {headless}")
    print("="*50)
    
    # 运行自动化
    success = asyncio.run(send_to_ai_studio(prompt, headless))
    
    if success:
        print("\n✅ 任务完成！")
    else:
        print("\n❌ 任务失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
