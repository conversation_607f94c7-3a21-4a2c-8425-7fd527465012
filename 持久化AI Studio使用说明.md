# 持久化Google AI Studio自动化工具

## 🎯 功能特点

- ✅ **持久化浏览器进程**: 启动后保持浏览器不关闭
- ✅ **自动登录**: 从配置文件读取Google账号密码，自动登录
- ✅ **智能检测**: 自动检测登录状态，已登录则直接使用
- ✅ **交互模式**: 支持连续输入多个提示词
- ✅ **响应获取**: 自动等待并尝试获取AI响应内容
- ✅ **持久化数据**: 使用独立的用户数据目录，保持登录状态

## 📁 文件说明

- `persistent_ai_studio.py` - 主程序文件
- `config.json` - 配置文件（包含Google账号信息）

## ⚙️ 配置设置

### 1. 编辑配置文件 `config.json`

```json
{
  "google_account": {
    "email": "<EMAIL>",        // 您的Google邮箱
    "password": "your_password_here"        // 您的Google密码
  },
  "browser_settings": {
    "headless": false,                      // 是否无头模式
    "window_size": "1920,1080",            // 浏览器窗口大小
    "user_data_dir": "chrome_ai_studio_data", // 用户数据目录
    "keep_alive": true                      // 保持浏览器存活
  },
  "ai_studio_settings": {
    "default_url": "https://aistudio.google.com/prompts/new_chat",
    "wait_timeout": 30,                     // 等待超时时间
    "response_wait_time": 10                // 响应等待时间
  }
}
```

### 2. 重要配置说明

- **email**: 必须填入您的真实Google邮箱
- **password**: 必须填入您的真实Google密码
- **user_data_dir**: 浏览器数据目录，首次运行后会保存登录状态
- **headless**: 设为false可以看到浏览器界面，便于调试

## 🚀 使用方法

### 1. 激活Python环境
```bash
conda activate google_ai_studio
```

### 2. 运行程序
```bash
python persistent_ai_studio.py
```

### 3. 程序运行流程

1. **启动浏览器**: 程序会启动Chrome浏览器
2. **检查登录状态**: 自动检测是否已登录
3. **自动登录**: 如需登录，会自动输入账号密码
4. **进入交互模式**: 可以连续输入提示词
5. **获取响应**: 自动等待并显示AI响应

### 4. 交互模式使用

```
📝 请输入提示词: 你好，请介绍一下自己
🚀 提示词发送成功
⏳ 等待AI响应 (10秒)...

🤖 AI响应:
----------------------------------------
你好！我是Google的AI助手Gemini...
----------------------------------------

📝 请输入提示词: quit  # 输入quit退出
👋 退出交互模式
```

## 🔧 故障排除

### 1. 配置文件错误
```
❌ 配置文件未找到: config.json
💡 请创建配置文件并填入您的Google账号信息
```
**解决方法**: 确保config.json文件存在且格式正确

### 2. ChromeDriver未安装
```
❌ 启动Chrome失败: 'chromedriver' executable needs to be in PATH
💡 请确保已安装ChromeDriver
```
**解决方法**: 
- 下载ChromeDriver: https://chromedriver.chromium.org/
- 将chromedriver.exe放到PATH环境变量中

### 3. 自动登录失败
```
❌ 自动登录失败: Message: no such element: Unable to locate element
💡 请手动登录或检查账号密码
```
**解决方法**: 
- 检查config.json中的邮箱密码是否正确
- 如果有二次验证，程序会提示手动完成

### 4. 找不到输入框
```
❌ 未找到输入框
```
**解决方法**: 
- 等待页面完全加载
- 检查是否在正确的AI Studio页面
- 手动刷新页面

## 💡 使用技巧

### 1. 首次使用
- 首次运行会创建用户数据目录
- 登录成功后，下次运行会自动保持登录状态
- 建议首次使用时关注浏览器界面，确保登录成功

### 2. 持久化使用
- 程序退出时选择不关闭浏览器
- 下次运行时会复用已打开的浏览器会话
- 可以在浏览器中手动操作，不影响程序功能

### 3. 批量处理
- 可以准备多个提示词，在交互模式中依次输入
- 每个提示词会自动发送并等待响应
- 支持长时间连续使用

### 4. 安全建议
- 配置文件包含敏感信息，请妥善保管
- 建议使用应用专用密码而非主密码
- 定期检查Google账号的登录活动

## 🔄 工作流程图

```
启动程序
    ↓
启动Chrome浏览器
    ↓
访问AI Studio
    ↓
检查登录状态
    ↓
[未登录] → 自动登录 → [登录成功]
    ↓
进入交互模式
    ↓
输入提示词 → 发送 → 等待响应 → 显示结果
    ↓
继续输入或退出
```

## 📞 支持

如果遇到问题，请检查：
1. Chrome浏览器是否正常安装
2. ChromeDriver版本是否与Chrome匹配
3. 网络连接是否正常
4. Google账号是否可以正常登录
5. 配置文件格式是否正确

---

**注意**: 此工具仅用于合法的自动化操作，请遵守Google的服务条款。
