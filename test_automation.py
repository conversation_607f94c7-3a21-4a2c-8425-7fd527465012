#!/usr/bin/env python3
"""
测试 Google AI Studio 自动化工具
"""

import asyncio
import sys
from quick_ai_studio import send_to_ai_studio


async def test_simple_prompt():
    """测试简单提示词"""
    print("🧪 测试1: 简单提示词")
    
    simple_prompt = "请简单介绍一下人工智能的发展历史。"
    
    success = await send_to_ai_studio(simple_prompt, headless=False)
    
    if success:
        print("✅ 测试1通过")
        return True
    else:
        print("❌ 测试1失败")
        return False


async def test_long_prompt():
    """测试长提示词"""
    print("\n🧪 测试2: 长提示词（默认哈佛大学分析）")
    
    long_prompt = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    success = await send_to_ai_studio(long_prompt, headless=False)
    
    if success:
        print("✅ 测试2通过")
        return True
    else:
        print("❌ 测试2失败")
        return False


async def test_headless_mode():
    """测试无头模式"""
    print("\n🧪 测试3: 无头模式")
    
    prompt = "请用一句话解释什么是机器学习。"
    
    success = await send_to_ai_studio(prompt, headless=True)
    
    if success:
        print("✅ 测试3通过")
        return True
    else:
        print("❌ 测试3失败")
        return False


def main():
    """主测试函数"""
    print("🚀 Google AI Studio 自动化工具测试")
    print("="*50)
    
    # 检查命令行参数
    test_mode = "all"
    if len(sys.argv) > 1:
        test_mode = sys.argv[1]
    
    async def run_tests():
        results = []
        
        if test_mode in ["all", "simple"]:
            results.append(await test_simple_prompt())
        
        if test_mode in ["all", "long"]:
            results.append(await test_long_prompt())
        
        if test_mode in ["all", "headless"]:
            results.append(await test_headless_mode())
        
        return results
    
    # 运行测试
    try:
        results = asyncio.run(run_tests())
        
        print("\n" + "="*50)
        print("📊 测试结果汇总:")
        
        passed = sum(results)
        total = len(results)
        
        print(f"✅ 通过: {passed}/{total}")
        print(f"❌ 失败: {total - passed}/{total}")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return 0
        else:
            print("⚠️ 部分测试失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1


def show_help():
    """显示帮助信息"""
    print("""
Google AI Studio 自动化工具测试

用法:
    python test_automation.py [测试模式]

测试模式:
    all      - 运行所有测试（默认）
    simple   - 只测试简单提示词
    long     - 只测试长提示词
    headless - 只测试无头模式

示例:
    python test_automation.py
    python test_automation.py simple
    python test_automation.py headless

注意:
    - 首次运行需要手动登录 Google 账户
    - 确保网络连接正常
    - 有界面测试会打开浏览器窗口
""")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_help()
    else:
        exit_code = main()
        sys.exit(exit_code)
