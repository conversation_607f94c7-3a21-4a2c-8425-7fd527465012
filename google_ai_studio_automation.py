#!/usr/bin/env python3
"""
Google AI Studio 自动化脚本
自动访问 https://aistudio.google.com/prompts/new_chat 并发送指定的提示词
"""

import asyncio
import time
from typing import Optional
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig


class GoogleAIStudioAutomator:
    """Google AI Studio 自动化操作类"""
    
    def __init__(self, headless: bool = False, timeout: int = 30000):
        """
        初始化自动化器
        
        Args:
            headless: 是否无头模式运行浏览器
            timeout: 页面操作超时时间（毫秒）
        """
        self.headless = headless
        self.timeout = timeout
        self.browser_config = BrowserConfig(
            browser_type="chromium",
            headless=headless,
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
    async def send_prompt_to_ai_studio(self, prompt_text: str, wait_for_response: bool = True) -> dict:
        """
        发送提示词到Google AI Studio
        
        Args:
            prompt_text: 要发送的提示词文本
            wait_for_response: 是否等待AI响应
            
        Returns:
            包含操作结果的字典
        """
        result = {
            "success": False,
            "message": "",
            "response_text": "",
            "error": None
        }
        
        try:
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # 第一步：访问Google AI Studio新对话页面
                print("🚀 正在访问 Google AI Studio...")
                
                crawler_config = CrawlerRunConfig(
                    page_timeout=self.timeout,
                    session_id="ai_studio_session",
                    delay_before_return_html=3000,  # 等待3秒让页面完全加载
                )
                
                initial_result = await crawler.arun(
                    url="https://aistudio.google.com/prompts/new_chat",
                    config=crawler_config
                )
                
                if not initial_result.success:
                    result["error"] = f"无法访问页面: {initial_result.error_message}"
                    return result
                
                print("✅ 页面加载成功")
                
                # 第二步：等待页面完全加载并查找输入框
                print("🔍 正在查找输入框...")
                
                # 使用JavaScript查找并填充输入框
                fill_input_js = f"""
                // 等待页面加载完成
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 尝试多种可能的输入框选择器
                const selectors = [
                    'textarea[placeholder*="Enter a prompt"]',
                    'textarea[placeholder*="Type a message"]',
                    'textarea[data-testid="prompt-textarea"]',
                    'textarea[aria-label*="prompt"]',
                    'textarea[aria-label*="message"]',
                    '.prompt-textarea textarea',
                    '[role="textbox"]',
                    'textarea:not([readonly])',
                    'div[contenteditable="true"]'
                ];
                
                let inputElement = null;
                for (const selector of selectors) {{
                    inputElement = document.querySelector(selector);
                    if (inputElement) {{
                        console.log('找到输入框:', selector);
                        break;
                    }}
                }}
                
                if (!inputElement) {{
                    throw new Error('未找到输入框');
                }}
                
                // 清空并填充文本
                if (inputElement.tagName.toLowerCase() === 'textarea') {{
                    inputElement.value = `{prompt_text.replace('`', '\\`').replace('${', '\\${')}`;
                    inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    inputElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
                }} else if (inputElement.contentEditable === 'true') {{
                    inputElement.textContent = `{prompt_text.replace('`', '\\`')}`;
                    inputElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                }}
                
                // 触发焦点事件
                inputElement.focus();
                
                return {{ success: true, message: '文本已填充到输入框' }};
                """
                
                fill_result = await crawler.arun(
                    url="https://aistudio.google.com/prompts/new_chat",
                    config=CrawlerRunConfig(
                        session_id="ai_studio_session",
                        js_code=[fill_input_js],
                        js_only=True,
                        delay_before_return_html=1000
                    )
                )
                
                print("✅ 提示词已填充到输入框")
                
                # 第三步：查找并点击发送按钮
                print("📤 正在发送提示词...")
                
                send_js = """
                // 等待一下确保文本已填充
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 尝试多种可能的发送按钮选择器
                const buttonSelectors = [
                    'button[aria-label*="Send"]',
                    'button[title*="Send"]',
                    'button:has(svg[data-testid="send-icon"])',
                    'button[data-testid="send-button"]',
                    'button:contains("Send")',
                    'button:contains("发送")',
                    '[role="button"][aria-label*="Send"]',
                    'button[type="submit"]'
                ];
                
                let sendButton = null;
                for (const selector of buttonSelectors) {
                    try {
                        sendButton = document.querySelector(selector);
                        if (sendButton && !sendButton.disabled) {
                            console.log('找到发送按钮:', selector);
                            break;
                        }
                    } catch (e) {
                        // 某些选择器可能不支持，继续尝试下一个
                        continue;
                    }
                }
                
                // 如果没找到，尝试通过文本内容查找
                if (!sendButton) {
                    const allButtons = document.querySelectorAll('button');
                    for (const btn of allButtons) {
                        const text = btn.textContent?.toLowerCase() || '';
                        if (text.includes('send') || text.includes('发送') || btn.querySelector('svg')) {
                            sendButton = btn;
                            console.log('通过文本找到发送按钮');
                            break;
                        }
                    }
                }
                
                if (!sendButton) {
                    throw new Error('未找到发送按钮');
                }
                
                // 点击发送按钮
                sendButton.click();
                
                return { success: true, message: '已点击发送按钮' };
                """
                
                send_result = await crawler.arun(
                    url="https://aistudio.google.com/prompts/new_chat",
                    config=CrawlerRunConfig(
                        session_id="ai_studio_session",
                        js_code=[send_js],
                        js_only=True,
                        delay_before_return_html=2000
                    )
                )
                
                print("✅ 提示词发送成功")
                
                # 第四步：等待AI响应（如果需要）
                if wait_for_response:
                    print("⏳ 等待AI响应...")
                    
                    # 等待响应出现
                    wait_response_js = """
                    // 等待AI响应出现
                    let attempts = 0;
                    const maxAttempts = 30; // 最多等待30秒
                    
                    while (attempts < maxAttempts) {
                        // 查找响应内容的可能选择器
                        const responseSelectors = [
                            '[data-testid="response-content"]',
                            '.response-content',
                            '.ai-response',
                            '[role="article"]',
                            '.message-content:last-child',
                            '.response-text'
                        ];
                        
                        let responseElement = null;
                        for (const selector of responseSelectors) {
                            responseElement = document.querySelector(selector);
                            if (responseElement && responseElement.textContent.trim()) {
                                return {
                                    success: true,
                                    response: responseElement.textContent.trim(),
                                    message: 'AI响应已获取'
                                };
                            }
                        }
                        
                        // 如果没找到，等待1秒后重试
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        attempts++;
                    }
                    
                    return {
                        success: false,
                        message: '等待AI响应超时',
                        response: ''
                    };
                    """
                    
                    response_result = await crawler.arun(
                        url="https://aistudio.google.com/prompts/new_chat",
                        config=CrawlerRunConfig(
                            session_id="ai_studio_session",
                            js_code=[wait_response_js],
                            js_only=True,
                            delay_before_return_html=1000
                        )
                    )
                    
                    # 尝试从页面内容中提取响应
                    if response_result.success and response_result.extracted_content:
                        try:
                            import json
                            js_result = json.loads(response_result.extracted_content)
                            if js_result.get("success"):
                                result["response_text"] = js_result.get("response", "")
                                print(f"✅ 获取到AI响应: {result['response_text'][:100]}...")
                            else:
                                print("⚠️ 未能获取到AI响应，可能需要更长时间")
                        except:
                            print("⚠️ 响应解析失败")
                
                result["success"] = True
                result["message"] = "提示词发送成功"
                
        except Exception as e:
            result["error"] = str(e)
            result["message"] = f"操作失败: {str(e)}"
            print(f"❌ 错误: {str(e)}")
        
        return result


async def main():
    """主函数"""
    # 要发送的提示词
    prompt_text = """请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式"""
    
    # 创建自动化器实例（设置为非无头模式以便观察过程）
    automator = GoogleAIStudioAutomator(headless=False, timeout=60000)
    
    print("🤖 开始自动化操作...")
    print(f"📝 提示词长度: {len(prompt_text)} 字符")
    
    # 发送提示词
    result = await automator.send_prompt_to_ai_studio(
        prompt_text=prompt_text,
        wait_for_response=True
    )
    
    # 输出结果
    print("\n" + "="*50)
    print("📊 操作结果:")
    print(f"✅ 成功: {result['success']}")
    print(f"💬 消息: {result['message']}")
    
    if result.get('response_text'):
        print(f"🤖 AI响应: {result['response_text'][:200]}...")
    
    if result.get('error'):
        print(f"❌ 错误: {result['error']}")
    
    print("="*50)


if __name__ == "__main__":
    asyncio.run(main())
