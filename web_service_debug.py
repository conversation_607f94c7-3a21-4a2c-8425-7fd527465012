#!/usr/bin/env python3
"""
Crawl4AI Web Service - 调试版本
显示详细错误信息，用于诊断exe打包问题
"""

import sys
import os
import time
import re
import traceback
from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, HttpUrl
import uvicorn
import asyncio

def remove_markdown_links(markdown_text: str) -> str:
    """移除Markdown文本中的所有超链接，保留链接文本"""
    if not markdown_text:
        return ""
    
    # 移除引用式链接定义（如：[1]: http://example.com "Title"）
    markdown_text = re.sub(r'^\s*\[.+?\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除内联链接，保留链接文本（如：[文本](http://example.com) -> 文本）
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用式链接，保留链接文本（如：[文本][1] -> 文本）
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除自动链接（如：<http://example.com>）
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除纯URL链接
    markdown_text = re.sub(r'(?<![\[\(])(https?://\S+)(?![\]\)])', '', markdown_text)
    
    # 清理多余的空行和空格
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    markdown_text = re.sub(r'[ \t]+', ' ', markdown_text)
    
    return markdown_text.strip()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    url: HttpUrl
    timeout: int = 60


class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    url: str
    markdown_content: str
    processing_time: float
    error: str = ""
    debug_info: dict = {}  # 调试信息


# 创建FastAPI应用
app = FastAPI(
    title="Crawl4AI Debug Web Service",
    description="调试版本 - 显示详细错误信息",
    version="1.0.0-debug"
)

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "message": "Crawl4AI Debug Web Service",
        "version": "1.0.0-debug",
        "description": "调试版本 - 用于诊断exe打包问题",
        "python_info": {
            "version": sys.version,
            "executable": sys.executable,
            "path": sys.path[:3]  # 只显示前3个路径
        }
    }

@app.get("/debug")
async def debug_info():
    """调试信息接口"""
    debug_data = {
        "python_version": sys.version,
        "python_executable": sys.executable,
        "working_directory": os.getcwd(),
        "environment_vars": {
            "PATH": os.environ.get("PATH", "")[:200],  # 只显示前200字符
            "PYTHONPATH": os.environ.get("PYTHONPATH", "Not set"),
        }
    }
    
    # 测试关键模块导入
    modules_status = {}
    test_modules = [
        "numpy", "playwright", "crawl4ai", "fake_useragent", 
        "fake_http_header", "playwright_stealth", "aiohttp", "bs4"
    ]
    
    for module_name in test_modules:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', '未知')
            modules_status[module_name] = {
                "status": "成功",
                "version": str(version),
                "path": getattr(module, '__file__', '未知')
            }
        except Exception as e:
            modules_status[module_name] = {
                "status": "失败",
                "error": str(e)
            }
    
    debug_data["modules"] = modules_status
    
    # 测试crawl4ai初始化
    crawl4ai_init = {}
    try:
        from crawl4ai import AsyncWebCrawler
        crawl4ai_init["AsyncWebCrawler_import"] = "成功"
        
        # 尝试创建实例
        crawler = AsyncWebCrawler()
        crawl4ai_init["instance_creation"] = "成功"
        
        # 尝试获取版本信息
        try:
            from crawl4ai.__version__ import __version__
            crawl4ai_init["version"] = __version__
        except Exception as e:
            crawl4ai_init["version_error"] = str(e)
            
    except Exception as e:
        crawl4ai_init["error"] = str(e)
        crawl4ai_init["traceback"] = traceback.format_exc()
    
    debug_data["crawl4ai_init"] = crawl4ai_init
    
    return debug_data

@app.post("/markdown", response_model=CrawlResponse)
async def crawl_to_markdown_debug(request: CrawlRequest):
    """
    调试版本的爬取接口 - 显示详细错误信息
    """
    start_time = time.time()
    debug_info = {
        "start_time": start_time,
        "request_url": str(request.url),
        "timeout": request.timeout
    }
    
    try:
        # 第一步：尝试导入crawl4ai
        debug_info["step1_import"] = "开始导入crawl4ai..."
        try:
            from crawl4ai import AsyncWebCrawler
            from crawl4ai.async_configs import CrawlerRunConfig
            debug_info["step1_import"] = "成功"
        except Exception as e:
            debug_info["step1_import"] = f"失败: {str(e)}"
            debug_info["step1_traceback"] = traceback.format_exc()
            raise e
        
        # 第二步：创建crawler实例
        debug_info["step2_create"] = "开始创建crawler实例..."
        try:
            crawler = AsyncWebCrawler()
            debug_info["step2_create"] = "成功"
        except Exception as e:
            debug_info["step2_create"] = f"失败: {str(e)}"
            debug_info["step2_traceback"] = traceback.format_exc()
            raise e
        
        # 第三步：尝试启动crawler
        debug_info["step3_start"] = "开始启动crawler..."
        try:
            await crawler.start()
            debug_info["step3_start"] = "成功"
        except Exception as e:
            debug_info["step3_start"] = f"失败: {str(e)}"
            debug_info["step3_traceback"] = traceback.format_exc()
            raise e
        
        # 第四步：配置爬取参数
        debug_info["step4_config"] = "配置爬取参数..."
        try:
            timeout_ms = request.timeout * 1000
            config = CrawlerRunConfig(
                page_timeout=timeout_ms,
                wait_until="domcontentloaded",
                verbose=True  # 启用详细日志
            )
            debug_info["step4_config"] = "成功"
        except Exception as e:
            debug_info["step4_config"] = f"失败: {str(e)}"
            debug_info["step4_traceback"] = traceback.format_exc()
            raise e
        
        # 第五步：执行爬取
        debug_info["step5_crawl"] = "开始执行爬取..."
        try:
            result = await crawler.arun(
                url=str(request.url),
                config=config
            )
            debug_info["step5_crawl"] = "成功"
            debug_info["result_success"] = result.success
            debug_info["result_status"] = getattr(result, 'status_code', 'unknown')
        except Exception as e:
            debug_info["step5_crawl"] = f"失败: {str(e)}"
            debug_info["step5_traceback"] = traceback.format_exc()
            raise e
        finally:
            # 确保关闭crawler
            try:
                await crawler.close()
                debug_info["crawler_closed"] = "成功"
            except Exception as e:
                debug_info["crawler_close_error"] = str(e)
        
        # 计算处理时间
        processing_time = round(time.time() - start_time, 3)
        debug_info["processing_time"] = processing_time
        
        # 检查爬取结果
        if result.success:
            # 处理markdown内容
            raw_markdown = ""
            if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                raw_markdown = result.markdown.raw_markdown or ""
            
            clean_markdown = remove_markdown_links(raw_markdown)
            
            return CrawlResponse(
                success=True,
                url=str(request.url),
                markdown_content=clean_markdown,
                processing_time=processing_time,
                error="",
                debug_info=debug_info
            )
        else:
            error_msg = getattr(result, 'error_message', '未知错误')
            debug_info["original_error"] = error_msg
            
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=error_msg,
                debug_info=debug_info
            )
            
    except Exception as e:
        processing_time = round(time.time() - start_time, 3)
        error_msg = f"系统错误: {str(e)}"
        debug_info["final_error"] = error_msg
        debug_info["final_traceback"] = traceback.format_exc()
        
        return CrawlResponse(
            success=False,
            url=str(request.url),
            markdown_content="",
            processing_time=processing_time,
            error=error_msg,
            debug_info=debug_info
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok", "message": "服务正常运行"}

if __name__ == "__main__":
    print("🚀 启动 Crawl4AI Debug Web Service...")
    print("📡 服务将在 http://localhost:5000 运行")
    print("📚 API文档可在 http://localhost:5000/docs 查看")
    print("🔍 /markdown 接口用于网页内容抓取（调试版本）")
    print("🐛 /debug 接口显示详细调试信息")
    print("=" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=5000) 