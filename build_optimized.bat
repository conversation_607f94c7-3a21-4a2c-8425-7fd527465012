@echo off
chcp 65001 >nul
echo ===============================================
echo   Crawl4AI Web Service 优化版打包脚本
echo ===============================================
echo.

:: 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

:: 显示Python版本
for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

:: 检查必要的包
echo.
echo 🔍 检查必要的Python包...

python -c "import pyinstaller" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller已就绪

python -c "import crawl4ai" 2>nul
if errorlevel 1 (
    echo ⚠️  警告：crawl4ai未安装，将只能使用备用模式
    set CRAWL4AI_AVAILABLE=0
) else (
    echo ✅ crawl4ai已安装
    set CRAWL4AI_AVAILABLE=1
)

python -c "import playwright" 2>nul
if errorlevel 1 (
    echo ⚠️  警告：playwright未安装，将只能使用备用模式
    set PLAYWRIGHT_AVAILABLE=0
) else (
    echo ✅ playwright已安装
    set PLAYWRIGHT_AVAILABLE=1
)

:: 选择打包版本
echo.
echo 📦 选择打包版本：
echo [1] 完整版 (包含Crawl4AI + Playwright，约80-100MB)
echo [2] 增强备用版 (requests + BeautifulSoup + 反反爬，约30-40MB)
echo [3] 轻量版 (仅基础requests，约20-25MB)
echo [4] 自动选择 (根据环境自动选择最佳版本)
echo.
set /p choice="请选择 [1-4]: "

if "%choice%"=="1" goto build_full
if "%choice%"=="2" goto build_enhanced
if "%choice%"=="3" goto build_light
if "%choice%"=="4" goto build_auto
echo ❌ 无效选择，默认使用自动选择
goto build_auto

:build_auto
echo.
echo 🤖 自动选择版本...
if %CRAWL4AI_AVAILABLE%==1 if %PLAYWRIGHT_AVAILABLE%==1 (
    echo ✅ 检测到完整环境，选择完整版
    goto build_full
) else (
    echo ⚠️  环境不完整，选择增强备用版
    goto build_enhanced
)

:build_full
echo.
echo 📦 开始构建完整版...
set "SPEC_FILE=crawl4ai-web-service-optimized.spec"
set "ENTRY_FILE=web_service_enhanced.py"
set "OUTPUT_NAME=crawl4ai-web-service-full"
goto start_build

:build_enhanced
echo.
echo 📦 开始构建增强备用版...
set "SPEC_FILE=crawl4ai-web-service-enhanced.spec"
set "ENTRY_FILE=web_service_enhanced.py"
set "OUTPUT_NAME=crawl4ai-web-service-enhanced"
goto create_enhanced_spec

:build_light
echo.
echo 📦 开始构建轻量版...
set "SPEC_FILE=crawl4ai-web-service-light.spec"
set "ENTRY_FILE=web_service_enhanced.py"
set "OUTPUT_NAME=crawl4ai-web-service-light"
goto create_light_spec

:create_enhanced_spec
echo 🔨 创建增强版spec文件...
(
echo # -*- mode: python ; coding: utf-8 -*-
echo """增强备用版 - 平衡功能和大小"""
echo.
echo hiddenimports = [
echo     'fastapi', 'uvicorn', 'starlette', 'pydantic',
echo     'requests', 'bs4', 'html2text', 'lxml',
echo     'urllib3', 'certifi', 'charset_normalizer',
echo     'aiohttp', 'aiofiles', 'asyncio',
echo     'json', 'os', 'sys', 're', 'time'
echo ]
echo.
echo excludes = [
echo     'playwright', 'crawl4ai', 'nltk', 'numpy', 
echo     'pandas', 'matplotlib', 'torch', 'tensorflow'
echo ]
echo.
echo a = Analysis^(['%ENTRY_FILE%'^], hiddenimports=hiddenimports, excludes=excludes^)
echo pyz = PYZ^(a.pure, a.zipped_data^)
echo exe = EXE^(pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
echo     name='%OUTPUT_NAME%', debug=False, console=True^)
) > "%SPEC_FILE%"
goto start_build

:create_light_spec
echo 🔨 创建轻量版spec文件...
(
echo # -*- mode: python ; coding: utf-8 -*-
echo """轻量版 - 最小化依赖"""
echo.
echo hiddenimports = [
echo     'fastapi', 'uvicorn', 'starlette', 'pydantic',
echo     'requests', 'json', 'os', 'sys', 're', 'time'
echo ]
echo.
echo excludes = [
echo     'playwright', 'crawl4ai', 'bs4', 'lxml', 'html2text',
echo     'nltk', 'numpy', 'pandas', 'matplotlib'
echo ]
echo.
echo a = Analysis^(['%ENTRY_FILE%'^], hiddenimports=hiddenimports, excludes=excludes^)
echo pyz = PYZ^(a.pure, a.zipped_data^)
echo exe = EXE^(pyz, a.scripts, a.binaries, a.zipfiles, a.datas, [],
echo     name='%OUTPUT_NAME%', debug=False, console=True^)
) > "%SPEC_FILE%"
goto start_build

:start_build
:: 清理旧的构建文件
echo.
echo 🧹 清理旧的构建文件...
if exist build rmdir /s /q build 2>nul
if exist dist\%OUTPUT_NAME%.exe del /q dist\%OUTPUT_NAME%.exe 2>nul

:: 开始构建
echo.
echo 🔨 开始PyInstaller构建...
echo 使用配置文件: %SPEC_FILE%
echo 入口文件: %ENTRY_FILE%
echo 输出名称: %OUTPUT_NAME%
echo.

pyinstaller --clean "%SPEC_FILE%"

:: 检查构建结果
if exist "dist\%OUTPUT_NAME%.exe" (
    echo.
    echo ✅ 构建成功！
    echo.
    echo 📁 输出位置: dist\%OUTPUT_NAME%.exe
    
    :: 显示文件大小
    for %%i in ("dist\%OUTPUT_NAME%.exe") do set FILE_SIZE=%%~zi
    set /a FILE_SIZE_MB=%FILE_SIZE%/1024/1024
    echo 📊 文件大小: %FILE_SIZE_MB% MB
    
    echo.
    echo 🚀 测试启动...
    echo 您可以运行以下命令测试：
    echo    dist\%OUTPUT_NAME%.exe
    echo.
    echo 🌐 服务启动后访问：
    echo    http://localhost:5000 - 主页
    echo    http://localhost:5000/docs - API文档
    echo.
    
    :: 询问是否立即测试
    set /p test_now="是否立即测试运行？(y/n): "
    if /i "%test_now%"=="y" (
        echo.
        echo 🚀 启动测试...
        echo 按Ctrl+C停止服务
        start /wait dist\%OUTPUT_NAME%.exe
    )
) else (
    echo.
    echo ❌ 构建失败！
    echo.
    echo 🔍 故障排除建议：
    echo 1. 检查Python版本 ^(建议3.9+^)
    echo 2. 更新pip: pip install --upgrade pip
    echo 3. 重新安装依赖: pip install -r requirements.txt
    echo 4. 查看上方错误信息进行调试
    echo.
    echo 📋 如需帮助，请查看build日志或联系支持
)

echo.
echo ===============================================
echo           构建过程完成
echo ===============================================
pause 