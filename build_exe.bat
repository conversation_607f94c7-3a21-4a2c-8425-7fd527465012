@echo off
chcp 65001 > nul
echo ================================================
echo 🛠️  Crawl4AI Web Service EXE 构建工具
echo ================================================
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装或未添加到 PATH
    echo 请先安装 Python 3.9+ 并确保添加到系统 PATH
    pause
    exit /b 1
)

echo ✓ Python 已安装
python --version

echo.
echo 📦 开始构建过程...
echo.

REM 安装或升级必要的包
echo 🔧 检查并安装必要的依赖...
python -m pip install --upgrade pip
python -m pip install pyinstaller

echo.
echo 🚀 运行构建脚本...
python build_exe.py

echo.
if exist "dist\crawl4ai-web-service.exe" (
    echo ✅ 构建成功！
    echo.
    echo 📁 EXE 文件位置: %cd%\dist\crawl4ai-web-service.exe
    echo.
    echo 💡 使用方法:
    echo    1. 双击运行 crawl4ai-web-service.exe
    echo    2. 或在命令行中运行: .\dist\crawl4ai-web-service.exe
    echo    3. 服务启动后访问: http://localhost:5000
    echo    4. API 文档: http://localhost:5000/docs
    echo.
    
    set /p choice="是否现在运行 EXE 文件进行测试？(y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 启动服务...
        start "" "dist\crawl4ai-web-service.exe"
        timeout /t 3 > nul
        echo.
        echo 🌐 正在打开浏览器...
        start "" "http://localhost:5000/docs"
    )
) else (
    echo ❌ 构建失败！
    echo 请检查上面的错误信息
)

echo.
pause 