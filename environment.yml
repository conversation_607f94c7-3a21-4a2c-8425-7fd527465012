name: crawl4ai-webservice
channels:
  - conda-forge
  - defaults
dependencies:
  # Python version
  - python>=3.8
  
  # Core dependencies available in conda
  - aiosqlite>=0.20,<0.21
  - lxml>=5.3,<5.4
  - numpy>=1.26.0,<3
  - pillow>=10.4,<10.5
  - playwright>=1.49.0
  - python-dotenv>=1.0,<1.1
  - requests>=2.31.0
  - beautifulsoup4>=4.12,<4.13
  - aiofiles>=24.1.0
  - colorama>=0.4,<0.5
  - pydantic>=2.10
  - pyopenssl>=24.3.0
  - psutil>=6.1.1
  - nltk>=3.9.1
  - rich>=13.9.4
  - cssselect>=1.2.0
  - chardet<5.0.0
  - brotli>=1.1.0
  - snowballstemmer>=2.2,<2.3
  
  # Web service dependencies
  - fastapi
  - uvicorn
  
  # pip dependencies (packages not available in conda or have conflicts)
  - pip
  - pip:
    - litellm>=1.53.1
    - tf-playwright-stealth>=1.1.0
    - xxhash>=3.4,<3.5
    - rank-bm25>=0.2,<0.3
    - chardet>=5.2.0 