# 使用现有浏览器访问Google AI Studio

## 问题说明
原来的脚本每次都会启动全新的浏览器实例，需要重新登录Google账号。现在提供了连接到已登录浏览器的解决方案。

## 解决方案

### 方法1：使用专用脚本（推荐）

1. **启动Chrome调试模式**
   ```bash
   # 双击运行
   start_chrome_debug.bat
   ```
   
   或手动启动：
   ```bash
   chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug
   ```

2. **在浏览器中登录**
   - Chrome会自动打开Google AI Studio页面
   - 登录您的Google账号
   - 确保可以正常访问AI Studio

3. **运行自动化脚本**
   ```bash
   # 激活conda环境
   conda activate google_ai_studio
   
   # 运行脚本
   python connect_existing_browser.py
   ```

### 方法2：修改现有脚本

如果您想继续使用原来的脚本，可以：

1. **先手动登录**
   - 正常打开Chrome浏览器
   - 登录Google AI Studio
   - 保持浏览器打开

2. **使用用户数据目录**
   - 脚本会使用您的默认Chrome配置
   - 保持登录状态

## 文件说明

- `connect_existing_browser.py` - 连接现有浏览器的自动化脚本
- `start_chrome_debug.bat` - 启动Chrome调试模式的批处理文件
- `quick_ai_studio.py` - 原始的自动化脚本（已更新）

## 优势

✅ **无需重复登录** - 使用已登录的浏览器会话
✅ **更快启动** - 不需要启动新浏览器实例  
✅ **保持状态** - 保留浏览器的所有设置和登录状态
✅ **可视化操作** - 可以实时看到自动化过程
✅ **手动干预** - 如果自动化失败，可以手动完成操作

## 故障排除

### 连接失败
```
❌ 连接失败: connect ECONNREFUSED 127.0.0.1:9222
```
**解决方案：**
- 确保Chrome已启动并开启调试端口
- 运行 `start_chrome_debug.bat`
- 或手动启动：`chrome.exe --remote-debugging-port=9222`

### 找不到输入框
```
❌ 未找到输入框
```
**解决方案：**
- 确保已登录Google AI Studio
- 手动访问：https://aistudio.google.com/prompts/new_chat
- 等待页面完全加载后再运行脚本

### 找不到发送按钮
```
❌ 未找到发送按钮
```
**解决方案：**
- 检查输入框是否已填充内容
- 确保页面完全加载
- 可以手动点击发送按钮

## 使用示例

```bash
# 1. 启动Chrome调试模式
start_chrome_debug.bat

# 2. 在浏览器中登录Google AI Studio

# 3. 激活Python环境
conda activate google_ai_studio

# 4. 运行自动化脚本
python connect_existing_browser.py

# 5. 使用自定义提示词文件
python connect_existing_browser.py my_prompt.txt
```

## 注意事项

⚠️ **端口占用** - 确保9222端口没有被其他程序占用
⚠️ **防火墙** - 可能需要允许Chrome访问本地端口
⚠️ **浏览器版本** - 建议使用最新版本的Chrome浏览器
⚠️ **网络连接** - 确保网络连接稳定，能够访问Google服务
