@echo off
chcp 65001 >nul
echo ========================================
echo Crawl4AI Web Service 高级重新构建
echo ========================================

echo.
echo 📋 可用的构建选项:
echo   1. 标准构建 (带NLTK禁用钩子)
echo   2. 无NLTK构建 (完全排除NLTK)
echo   3. 清理并重新安装依赖
echo   4. 查看当前依赖冲突
echo.

set /p choice=请选择构建选项 (1-4): 

echo.

if "%choice%"=="1" (
    echo 🚀 开始标准构建...
    goto :standard_build
) else if "%choice%"=="2" (
    echo 🚀 开始无NLTK构建...
    goto :no_nltk_build
) else if "%choice%"=="3" (
    echo 🧹 清理并重新安装依赖...
    goto :clean_install
) else if "%choice%"=="4" (
    echo 🔍 检查依赖冲突...
    goto :check_conflicts
) else (
    echo ❌ 无效选项，使用默认构建
    goto :standard_build
)

:standard_build
echo 🧹 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo 🔧 设置环境变量...
set OMP_NUM_THREADS=1
set OPENBLAS_NUM_THREADS=1
set MKL_NUM_THREADS=1
set VECLIB_MAXIMUM_THREADS=1
set NUMEXPR_NUM_THREADS=1
set MKL_THREADING_LAYER=SEQUENTIAL
set NUMPY_DISABLE_CPU_FEATURES=ALL

echo 📦 使用标准spec文件构建...
python -m PyInstaller --clean --noconfirm crawl4ai-web-service.spec
goto :check_result

:no_nltk_build
echo 🧹 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo 🔧 设置环境变量...
set OMP_NUM_THREADS=1
set OPENBLAS_NUM_THREADS=1
set MKL_NUM_THREADS=1
set VECLIB_MAXIMUM_THREADS=1
set NUMEXPR_NUM_THREADS=1
set MKL_THREADING_LAYER=SEQUENTIAL
set NUMPY_DISABLE_CPU_FEATURES=ALL

echo 📦 使用无NLTK spec文件构建...
python -m PyInstaller --clean --noconfirm crawl4ai-no-nltk.spec
goto :check_result

:clean_install
echo 🧹 卸载可能冲突的包...
pip uninstall -y nltk numpy
echo 📦 重新安装numpy...
pip install numpy==1.24.3
echo ✅ 依赖清理完成
goto :end

:check_conflicts
echo 🔍 检查NumPy版本...
python -c "import numpy; print(f'NumPy版本: {numpy.__version__}')"
echo.
echo 🔍 检查NLTK安装...
python -c "try: import nltk; print(f'NLTK版本: {nltk.__version__}'); except: print('NLTK未安装')"
echo.
echo 🔍 检查是否在源码目录...
python -c "import os; print(f'当前目录: {os.getcwd()}'); print('是否包含numpy源码:', os.path.exists('numpy'))"
goto :end

:check_result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功！
    echo.
    if exist "dist\crawl4ai-web-service.exe" (
        echo 📁 标准版本: dist\crawl4ai-web-service.exe
    )
    if exist "dist\crawl4ai-web-service-no-nltk.exe" (
        echo 📁 无NLTK版本: dist\crawl4ai-web-service-no-nltk.exe
    )
    echo.
    echo 🚀 是否要测试运行? (y/n)
    set /p test_choice=
    if /i "%test_choice%"=="y" (
        echo 启动测试...
        cd dist
        if exist "crawl4ai-web-service.exe" (
            crawl4ai-web-service.exe
        ) else if exist "crawl4ai-web-service-no-nltk.exe" (
            crawl4ai-web-service-no-nltk.exe
        )
    )
) else (
    echo.
    echo ❌ 构建失败！
    echo.
    echo 💡 建议尝试:
    echo   1. 使用选项3清理依赖
    echo   2. 使用选项2无NLTK构建
    echo   3. 检查Python环境是否在conda/虚拟环境中
)

:end
echo.
echo 按任意键退出...
pause >nul 