@font-face {
    font-family: "Monaco";
    font-style: normal;
    font-weight: normal;
    src: local("Monaco"), url("Monaco.woff") format("woff");
}

:root {
    --global-font-size: 14px;
    --global-code-font-size: 13px;
    --global-line-height: 1.5em;
    --global-space: 10px;
    --font-stack: Menlo, Monaco, Lucida Con<PERSON>e, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono,
        Courier New, monospace, serif;
    --font-stack: dm, Monaco, Courier New, monospace, serif;
    --mono-font-stack: Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono,
        Courier New, monospace, serif;

    --background-color: #151515; /* Dark background */
    --font-color: #eaeaea; /* Light font color for contrast */
    --invert-font-color: #151515; /* Dark color for inverted elements */
    --primary-color: #1a95e0; /* Primary color can remain the same or be adjusted for better contrast */
    --secondary-color: #727578; /* Secondary color for less important text */
    --secondary-dimmed-color: #8b857a; /* Dimmed secondary color */
    --error-color: #ff5555; /* Bright color for errors */
    --progress-bar-background: #444; /* Darker background for progress bar */
    --progress-bar-fill: #1a95e0; /* Bright color for progress bar fill */
    --code-bg-color: #1e1e1e; /* Darker background for code blocks */
    --input-style: solid; /* Keeping input style solid */
    --block-background-color: #202020; /* Darker background for block elements */
    --global-font-color: #eaeaea; /* Light font color for global elements */

    --background-color: #222225;

    --background-color: #070708;
    --page-width: 70em;
    --font-color: #e8e9ed;
    --invert-font-color: #222225;
    --secondary-color: #a3abba;
    --secondary-color: #d5cec0;
    --tertiary-color: #a3abba;
    --primary-dimmed-color: #09b5a5; /* Updated to the brand color */
    --primary-color: #50ffff; /* Updated to the brand color */
    --accent-color: rgb(243, 128, 245);
    --error-color: #ff3c74;
    --progress-bar-background: #3f3f44;
    --progress-bar-fill: #09b5a5; /* Updated to the brand color */
    --code-bg-color: #3f3f44;
    --input-style: solid;
    --display-h1-decoration: none;

    --display-h1-decoration: none;

    --header-height: 65px; /* Adjust based on your actual header height */
    --sidebar-width: 280px; /* Adjust based on your desired sidebar width */
    --toc-width: 240px; /* Adjust based on your desired ToC width */
    --layout-transition-speed: 0.2s; /* For potential future animations */

    --page-width : 100em; /* Adjust based on your design */
}



/* body {
    background-color: var(--background-color);
    color: var(--font-color);
}

a {
    color: var(--primary-color);
}

a:hover {
    background-color: var(--primary-color);
    color: var(--invert-font-color);
}

blockquote::after {
    color: #444; 
}

pre, code {
    background-color: var(--code-bg-color);
    color: var(--font-color);
}

.terminal-nav:first-child {
    border-bottom: 1px dashed var(--secondary-color);
} */

.terminal-mkdocs-main-content {
    line-height: var(--global-line-height);
}

strong {
    /* color : var(--primary-dimmed-color); */
    /* background-color: #50ffff17; */
    text-shadow: 0 0 0px var(--font-color), 0 0 0px var(--font-color);
}

.highlight {
    /* background: url(//s2.svgbox.net/pen-brushes.svg?ic=brush-1&color=50ffff); */
    background-color: #50ffff17;
    
}

div.highlight {
 margin-bottom: 2em;   
}

.terminal-card > header {
    color: var(--font-color);
    text-align: center;
    background-color: var(--progress-bar-background);
    padding: 0.3em 0.5em;
}
.btn.btn-sm {
    color: var(--font-color);
    padding: 0.2em 0.5em;
    font-size: 0.8em;
}

.loading-message {
    display: none;
    margin-top: 20px;
}

.response-section {
    display: none;
    padding-top: 20px;
}

.tabs {
    display: flex;
    flex-direction: column;
}
.tab-list {
    display: flex;
    padding: 0;
    margin: 0;
    list-style-type: none;
    border-bottom: 1px solid var(--font-color);
}
.tab-item {
    cursor: pointer;
    padding: 10px;
    border: 1px solid var(--font-color);
    margin-right: -1px;
    border-bottom: none;
}
.tab-item:hover,
.tab-item:focus,
.tab-item:active {
    background-color: var(--progress-bar-background);
}
.tab-content {
    display: none;
    border: 1px solid var(--font-color);
    border-top: none;
}
.tab-content:first-of-type {
    display: block;
}

.tab-content header {
    padding: 0.5em;
    display: flex; 
    justify-content: end; 
    align-items: center;
    background-color: var(--progress-bar-background);
}
.tab-content pre {
    margin: 0;
    max-height: 300px; overflow: auto; border:none;
}

ol li::before {
    content: counters(item, ".") ". ";
    counter-increment: item;
    /* float: left; */
    /* padding-right: 5px; */
}


/* 8 TERMINAL CSS */

.terminal code {
    font-size: var(--global-code-font-size);
    background: var(--block-background-color);
    /* color: var(--secondary-color); */
    color: var(--primary-dimmed-color);
}

.terminal pre code {
    background: var(--block-background-color);
    color: var(--secondary-color);
}

.hljs-keyword, .hljs-selector-tag, .hljs-built_in, .hljs-name, .hljs-tag {
    color: var(--accent-color);
}
.hljs-string {
    color: var(--primary-dimmed-color);
}
.hljs-comment {
    color: var(--secondary-dimmed-color);
    font-style: italic;
    font-size: 0.9em;
}
.hljs-number {
    color: var(--primary-dimmed-color);
}

.terminal strong > code, .terminal h2 > code , .terminal h3 > code {
    background-color: transparent;
    /* color: var(--font-color); */
    color: var(--primary-dimmed-color);
    text-shadow: none;
}

blockquote {
    background-color: var(--invert-font-color);
    padding: 1em 2em;
    border-left: 2px solid var(--primary-dimmed-color);
}

blockquote::after {
    content: "💡";
    white-space: pre;
    position: absolute;
    top: 1em;
    left: 5px;
    line-height: var(--global-line-height);
    color: #9ca2ab;
}

pre {
    display: block;
    word-break: break-word;
    word-wrap: break-word;
}

.terminal h1 {
    font-size: 2em;
}

.terminal h2 {
    font-size: 1.5em;
    margin-bottom: 0.8em;
}

.terminal h3 {
    font-size: 1.3em;
    margin-bottom: 0.8em;
}

.terminal h1, .terminal h2, .terminal h3, .terminal h4, .terminal h5, .terminal h6 {
    text-shadow: 0 0 0px var(--font-color), 0 0 0px var(--font-color), 0 0 0px var(--font-color);
}

/* Lower max height or width for these images */
div.badges a {
    /* no underline */
    text-decoration: none !important;
}
div.badges a > img {
    width: auto;
}


table td, table th {
    border: 1px solid var(--code-bg-color) !important;
}