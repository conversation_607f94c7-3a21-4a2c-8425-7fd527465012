#!/usr/bin/env python3
"""
fake_http_header 专用打包钩子
确保fake_http_header包的数据文件被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有fake_http_header子模块
hiddenimports = collect_submodules('fake_http_header')

# 添加可能缺失的模块
hiddenimports.extend([
    'fake_http_header.data',
    'fake_http_header.constants',
    'fake_http_header.fake_http_header',
])

# 收集fake_http_header的数据文件
datas = collect_data_files('fake_http_header')

print(f"fake_http_header打包钩子: 包含了 {len(hiddenimports)} 个模块, {len(datas)} 个数据文件") 