#!/usr/bin/env python3
"""
fake_useragent 专用打包钩子
确保fake_useragent包的数据文件被正确包含
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有fake_useragent子模块
hiddenimports = collect_submodules('fake_useragent')

# 添加可能缺失的模块
hiddenimports.extend([
    'fake_useragent.data',
    'fake_useragent.utils',
    'fake_useragent.settings',
])

# 收集fake_useragent的数据文件
datas = collect_data_files('fake_useragent')

print(f"fake_useragent打包钩子: 包含了 {len(hiddenimports)} 个模块, {len(datas)} 个数据文件") 