#!/usr/bin/env python3
"""
NLTK 禁用钩子
防止 NLTK 运行时钩子导致的 NumPy CPU dispatcher 冲突
"""

import sys
import os

print("NLTK禁用钩子已应用")

# 在模块系统中标记NLTK钩子为已执行
sys.modules['pyi_rth_nltk'] = type(sys)('pyi_rth_nltk')

# 如果NLTK已经被导入，尝试清理它的状态
if 'nltk' in sys.modules:
    try:
        nltk_module = sys.modules['nltk']
        # 清理可能导致冲突的属性
        problematic_attrs = ['corpus', 'data', 'download']
        for attr in problematic_attrs:
            if hasattr(nltk_module, attr):
                print(f"清理NLTK属性: {attr}")
                delattr(nltk_module, attr)
    except Exception as e:
        print(f"NLTK清理警告: {e}")

# 阻止NLTK数据下载
os.environ['NLTK_DATA'] = ''

print("NLTK钩子已被禁用") 