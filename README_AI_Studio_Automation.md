# Google AI Studio 自动化工具

这个工具可以自动访问 Google AI Studio 并发送指定的提示词，支持两种使用方式。

## 文件说明

1. **`google_ai_studio_automation.py`** - 完整版本，包含详细的错误处理和响应获取
2. **`quick_ai_studio.py`** - 简化版本，专注于核心功能，推荐日常使用

## 环境要求

确保已安装必要的依赖：

```bash
# 安装 crawl4ai
pip install crawl4ai

# 安装浏览器依赖
crawl4ai-setup

# 如果遇到浏览器问题，手动安装
python -m playwright install --with-deps chromium
```

## 使用方法

### 方法1：使用默认提示词

```bash
# 有界面模式（推荐，可以看到操作过程）
python quick_ai_studio.py

# 无头模式（后台运行）
python quick_ai_studio.py --headless
```

### 方法2：使用自定义提示词文件

```bash
# 创建提示词文件
echo "你的提示词内容" > my_prompt.txt

# 运行脚本
python quick_ai_studio.py my_prompt.txt

# 无头模式
python quick_ai_studio.py my_prompt.txt --headless
```

### 方法3：使用完整版本

```python
import asyncio
from google_ai_studio_automation import GoogleAIStudioAutomator

async def main():
    automator = GoogleAIStudioAutomator(headless=False)
    result = await automator.send_prompt_to_ai_studio("你的提示词")
    print(result)

asyncio.run(main())
```

## 默认提示词

脚本默认使用以下提示词（分析哈佛大学相关部门联系人信息）：

```
请严格按以下要求分析网站 harvard.edu 及其子域名页面，请给我真实的数据（不要伪造）:

1. 仅扫描以下部门最近3年的相关页面:
   - Study Abroad
   - Office of International Education
   - Global Experience Office
   - International Programs Center

2. 仅提取匹配以下职位的人员信息:
   - Director
   - Associate Director
   - Faculty-led program director

3. 每个联系人包含以下字段，未找到的字段允许为空:
   - contactName: 全名 (如无则留空)
   - position: 精确职位名称
   - department: 所属部门 
   - email: 邮箱地址 (每个人的邮箱应该不同)
   - phone: 电话号码 (含国家代码)
   - url: 信息来源页面的完整URL

4. 特殊规则:
   - 若同一页面发现多个联系人, 分别记录
   - 若信息分散在多页面, 不同页面分别记录

5. 输出格式:
   - 表格形式
```

## 工作原理

1. **启动浏览器** - 使用 Chromium 浏览器（可选无头模式）
2. **访问页面** - 自动导航到 `https://aistudio.google.com/prompts/new_chat`
3. **查找输入框** - 智能识别多种可能的输入框选择器
4. **填充文本** - 将提示词填充到输入框
5. **发送请求** - 自动点击发送按钮
6. **等待响应** - 可选择等待并获取AI响应

## 注意事项

### 登录要求
- 首次使用需要手动登录 Google 账户
- 建议先在浏览器中登录 Google AI Studio，脚本会复用登录状态

### 网络要求
- 需要稳定的网络连接
- 可能需要科学上网工具访问 Google 服务

### 浏览器兼容性
- 默认使用 Chromium 浏览器
- 支持 Firefox 和 WebKit（需要修改配置）

### 超时设置
- 默认页面超时：60秒
- 可以根据网络情况调整超时时间

## 故障排除

### 常见问题

1. **找不到输入框**
   - 页面可能还在加载，增加等待时间
   - Google AI Studio 界面可能有更新，需要更新选择器

2. **发送按钮无法点击**
   - 确保文本已正确填充
   - 检查按钮是否被禁用

3. **浏览器启动失败**
   - 运行 `crawl4ai-setup` 重新安装浏览器
   - 检查系统权限

4. **网络连接问题**
   - 确保可以访问 Google 服务
   - 检查代理设置

### 调试模式

使用有界面模式可以观察整个操作过程：

```bash
python quick_ai_studio.py  # 不加 --headless 参数
```

## 自定义配置

可以修改脚本中的以下参数：

```python
# 浏览器配置
browser_config = BrowserConfig(
    browser_type="chromium",  # 浏览器类型
    headless=False,           # 是否无头模式
    viewport_width=1920,      # 视口宽度
    viewport_height=1080,     # 视口高度
    user_agent="..."          # 用户代理
)

# 超时设置
page_timeout=60000  # 页面超时（毫秒）
delay_before_return_html=5000  # 页面加载等待时间
```

## 扩展功能

### 批量处理

可以扩展脚本支持批量处理多个提示词：

```python
prompts = [
    "提示词1",
    "提示词2", 
    "提示词3"
]

for prompt in prompts:
    result = await automator.send_prompt_to_ai_studio(prompt)
    # 处理结果
```

### 响应保存

可以将AI响应保存到文件：

```python
if result.get('response_text'):
    with open('ai_response.txt', 'w', encoding='utf-8') as f:
        f.write(result['response_text'])
```

## 许可证

本工具基于现有的 crawl4ai 框架开发，请遵循相应的开源许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具。
