#!/usr/bin/env python3
"""
Crawl4AI Web Service - 增强版
智能降级机制 + 更好的反反爬功能
"""

import asyncio
import time
import re
import sys
import os
import random
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional
from pydantic import BaseModel, HttpUrl
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
import uvicorn


def remove_markdown_links(markdown_text: str) -> str:
    """移除markdown文本中的所有超链接和反爬虫干扰文本"""
    if not markdown_text:
        return markdown_text
    
    # 移除常见的反爬虫干扰文本
    markdown_text = re.sub(r'hsdfsf', '', markdown_text, flags=re.IGNORECASE)
    
    # 移除其他常见的干扰模式
    markdown_text = re.sub(r'(?i)[a-z]{5,}(?=[a-z]*@)', 
                          lambda m: '' if len(set(m.group())) <= 2 else m.group(), 
                          markdown_text)
    
    # 修复被干扰的邮箱地址格式
    markdown_text = re.sub(r'(\w+)@(\w+)\.(\w+)', r'\1@\2.\3', markdown_text)
    
    # 移除图片链接 ![alt](url) -> alt
    markdown_text = re.sub(r'!\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除普通链接 [text](url) -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\([^)]*\)', r'\1', markdown_text)
    
    # 移除引用链接 [text][ref] -> text
    markdown_text = re.sub(r'\[([^\]]*)\]\[[^\]]*\]', r'\1', markdown_text)
    
    # 移除引用定义 [ref]: url
    markdown_text = re.sub(r'^\s*\[([^\]]+)\]:\s*\S+.*$', '', markdown_text, flags=re.MULTILINE)
    
    # 移除直接URL链接 <url>
    markdown_text = re.sub(r'<https?://[^>]+>', '', markdown_text)
    
    # 移除自动链接（裸URL）
    markdown_text = re.sub(r'https?://\S+', '', markdown_text)
    
    # 清理多余的空行
    markdown_text = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_text)
    
    # 清理多余的空格
    markdown_text = re.sub(r'[ ]{3,}', '  ', markdown_text)
    
    return markdown_text.strip()


class CrawlRequest(BaseModel):
    """爬取请求模型"""
    url: HttpUrl
    timeout: int = 60
    use_fallback: bool = False  # 可选择强制使用备用方案


class CrawlResponse(BaseModel):
    """爬取响应模型"""
    success: bool
    url: str
    markdown_content: str
    processing_time: float
    error: str = ""
    method_used: str = ""  # 新增：使用的抓取方法


# 全局状态
crawler_state = {
    "primary_crawler": None,
    "mode": "INIT",  # INIT, PRIMARY, FALLBACK, ERROR
    "init_attempts": 0,
    "max_init_attempts": 3
}


async def enhanced_fallback_crawl(url: str, timeout: int = 60) -> Dict[str, Any]:
    """增强版备用爬取方案，包含反反爬机制"""
    try:
        import requests
        from bs4 import BeautifulSoup
        import html2text
        
        # 轮换User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
        
        # 创建session以保持cookies
        session = requests.Session()
        session.headers.update(headers)
        
        # 设置重试机制
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 执行请求
        response = session.get(url, timeout=timeout, allow_redirects=True)
        response.raise_for_status()
        
        # 检查内容类型
        content_type = response.headers.get('content-type', '').lower()
        if 'html' not in content_type:
            return {
                "success": False,
                "markdown_content": "",
                "error": f"不支持的内容类型: {content_type}"
            }
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 移除脚本和样式标签
        for script in soup(["script", "style", "noscript"]):
            script.decompose()
        
        # 移除注释
        from bs4 import Comment
        comments = soup.findAll(text=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
        
        # 转换为markdown
        h = html2text.HTML2Text()
        h.ignore_links = False
        h.ignore_images = True
        h.body_width = 0  # 不限制行宽
        h.unicode_snob = True
        markdown_content = h.handle(str(soup))
        
        return {
            "success": True,
            "markdown_content": markdown_content,
            "error": ""
        }
        
    except requests.exceptions.Timeout:
        return {
            "success": False,
            "markdown_content": "",
            "error": f"请求超时：{timeout}秒"
        }
    except requests.exceptions.HTTPError as e:
        status_code = e.response.status_code if e.response else 0
        if status_code == 403:
            return {
                "success": False,
                "markdown_content": "",
                "error": "访问被拒绝：网站可能有反爬虫保护"
            }
        elif status_code == 404:
            return {
                "success": False,
                "markdown_content": "",
                "error": "页面不存在"
            }
        else:
            return {
                "success": False,
                "markdown_content": "",
                "error": f"HTTP错误 {status_code}: {str(e)}"
            }
    except Exception as e:
        error_msg = str(e)
        if "connection" in error_msg.lower():
            error_msg = "网络连接失败"
        elif "ssl" in error_msg.lower():
            error_msg = "SSL证书验证失败"
        return {
            "success": False,
            "markdown_content": "",
            "error": f"备用抓取失败: {error_msg}"
        }


async def init_primary_crawler():
    """初始化主爬虫"""
    global crawler_state
    
    try:
        # 检测是否在exe环境中
        is_exe = getattr(sys, 'frozen', False)
        if is_exe:
            # print("🔍 检测到exe环境，可能无法使用完整Playwright功能")
            crawler_state["mode"] = "FALLBACK"
            return False
        
        # 尝试导入并初始化crawl4ai
        from crawl4ai import AsyncWebCrawler
        from crawl4ai.async_configs import BrowserConfig
        
        browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        crawler = AsyncWebCrawler(config=browser_config)
        await crawler.start()
        
        crawler_state["primary_crawler"] = crawler
        crawler_state["mode"] = "PRIMARY"
        print("✅ Crawl4AI主爬虫初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 主爬虫初始化失败: {e}")
        crawler_state["mode"] = "FALLBACK"
        return False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global crawler_state
    
    print("🚀 启动 Crawl4AI Web Service Enhanced...")
    
    # 尝试初始化主爬虫
    success = await init_primary_crawler()
    
    if success:
        print("📡 服务模式: 完整功能 (Crawl4AI + 备用)")
    else:
        print("📡 服务模式: 备用模式 (requests + BeautifulSoup)")
    
    print("📚 服务地址: http://localhost:5000")
    print("📚 API文档: http://localhost:5000/docs")
    
    yield
    
    # 清理资源
    if crawler_state["primary_crawler"]:
        try:
            await crawler_state["primary_crawler"].close()
            print("✅ 主爬虫已关闭")
        except Exception as e:
            print(f"❌ 关闭主爬虫时出错: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="Crawl4AI Web Service Enhanced",
    description="增强版网页内容抓取服务，具备智能降级和反反爬机制",
    version="2.0.0",
    docs_url=None,
    redoc_url=None,
    lifespan=lifespan
)


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url or "/openapi.json",
        title=app.title + " - API文档",
        swagger_js_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/5.0.0/swagger-ui.css",
    )


@app.get("/")
async def root():
    """根路径，返回服务信息"""
    global crawler_state
    
    return {
        "service": "Crawl4AI Web Service Enhanced",
        "version": "2.0.0",
        "crawler_mode": crawler_state["mode"],
        "description": "增强版网页内容抓取服务，具备智能降级和反反爬机制",
        "endpoints": {
            "/markdown": "POST - 网页内容抓取并转换为Markdown格式",
            "/status": "GET - 查看详细服务状态",
            "/health": "GET - 健康检查"
        },
        "features": [
            "智能抓取模式选择",
            "反反爬虫机制",
            "自动降级备用方案",
            "链接清理和内容优化"
        ]
    }


@app.get("/status")
async def detailed_status():
    """详细的服务状态检查"""
    global crawler_state
    
    status = {
        "service_status": "running",
        "crawler_mode": crawler_state["mode"],
        "primary_crawler_available": crawler_state["primary_crawler"] is not None,
        "fallback_available": True,
        "init_attempts": crawler_state["init_attempts"],
        "python_version": sys.version,
        "is_exe_environment": getattr(sys, 'frozen', False),
        "working_directory": os.getcwd()
    }
    
    # 测试功能
    if crawler_state["mode"] == "PRIMARY" and crawler_state["primary_crawler"]:
        try:
            if hasattr(crawler_state["primary_crawler"], 'ready') and crawler_state["primary_crawler"].ready:
                status["primary_crawler_ready"] = True
                status["test_result"] = "primary_ready"
            else:
                status["primary_crawler_ready"] = False
                status["test_result"] = "primary_not_ready"
        except Exception as e:
            status["primary_crawler_ready"] = False
            status["test_result"] = f"primary_error: {str(e)}"
    else:
        status["primary_crawler_ready"] = False
        status["test_result"] = "fallback_only"
    
    return status


@app.post("/markdown", response_model=CrawlResponse)
async def crawl_to_markdown_enhanced(request: CrawlRequest):
    """
    增强版爬取接口，智能选择抓取方法
    """
    global crawler_state
    start_time = time.time()
    
    # 强制使用备用方案
    if request.use_fallback or crawler_state["mode"] == "FALLBACK":
        print(f"🔄 使用备用方案抓取: {request.url}")
        
        fallback_result = await enhanced_fallback_crawl(str(request.url), request.timeout)
        processing_time = round(time.time() - start_time, 3)
        
        if fallback_result["success"]:
            clean_markdown = remove_markdown_links(fallback_result["markdown_content"])
            return CrawlResponse(
                success=True,
                url=str(request.url),
                markdown_content=clean_markdown,
                processing_time=processing_time,
                error="",
                method_used="enhanced_fallback"
            )
        else:
            return CrawlResponse(
                success=False,
                url=str(request.url),
                markdown_content="",
                processing_time=processing_time,
                error=fallback_result["error"],
                method_used="enhanced_fallback"
            )
    
    # 尝试使用主爬虫
    if crawler_state["mode"] == "PRIMARY" and crawler_state["primary_crawler"]:
        try:
            from crawl4ai.async_configs import CrawlerRunConfig
            
            timeout_ms = request.timeout * 1000
            config = CrawlerRunConfig(
                page_timeout=timeout_ms,
                wait_until="domcontentloaded",
                verbose=False
            )
            
            crawl_result = await crawler_state["primary_crawler"].arun(
                url=str(request.url),
                config=config
            )
            
            # 处理arun返回的生成器
            result = None
            if hasattr(crawl_result, '__aiter__'):
                async for res in crawl_result:
                    result = res
                    break
            else:
                result = crawl_result
            
            processing_time = round(time.time() - start_time, 3)
            
            if result and result.success:
                raw_markdown = ""
                if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                    raw_markdown = result.markdown.raw_markdown or ""
                
                clean_markdown = remove_markdown_links(raw_markdown)
                
                return CrawlResponse(
                    success=True,
                    url=str(request.url),
                    markdown_content=clean_markdown,
                    processing_time=processing_time,
                    error="",
                    method_used="crawl4ai"
                )
            else:
                # 主爬虫失败，自动降级到备用方案
                print(f"⚠️ 主爬虫失败，自动降级到备用方案: {request.url}")
                fallback_result = await enhanced_fallback_crawl(str(request.url), request.timeout)
                
                if fallback_result["success"]:
                    clean_markdown = remove_markdown_links(fallback_result["markdown_content"])
                    return CrawlResponse(
                        success=True,
                        url=str(request.url),
                        markdown_content=clean_markdown,
                        processing_time=processing_time,
                        error="主抓取失败，已使用备用方案",
                        method_used="auto_fallback"
                    )
                else:
                    error_msg = getattr(result, 'error_message', '抓取失败') if result else "抓取失败"
                    return CrawlResponse(
                        success=False,
                        url=str(request.url),
                        markdown_content="",
                        processing_time=processing_time,
                        error=f"主抓取和备用抓取均失败: {error_msg}",
                        method_used="both_failed"
                    )
        
        except Exception as e:
            # 主爬虫异常，自动降级到备用方案
            print(f"⚠️ 主爬虫异常，自动降级到备用方案: {e}")
            fallback_result = await enhanced_fallback_crawl(str(request.url), request.timeout)
            processing_time = round(time.time() - start_time, 3)
            
            if fallback_result["success"]:
                clean_markdown = remove_markdown_links(fallback_result["markdown_content"])
                return CrawlResponse(
                    success=True,
                    url=str(request.url),
                    markdown_content=clean_markdown,
                    processing_time=processing_time,
                    error="主抓取异常，已使用备用方案",
                    method_used="exception_fallback"
                )
            else:
                return CrawlResponse(
                    success=False,
                    url=str(request.url),
                    markdown_content="",
                    processing_time=processing_time,
                    error=f"主抓取异常且备用抓取失败: {str(e)}",
                    method_used="both_failed"
                )
    
    # 如果到这里，说明没有可用的抓取方法
    processing_time = round(time.time() - start_time, 3)
    return CrawlResponse(
        success=False,
        url=str(request.url),
        markdown_content="",
        processing_time=processing_time,
        error="没有可用的抓取方法",
        method_used="none"
    )


@app.get("/health")
async def health_check():
    """健康检查接口"""
    global crawler_state
    
    if crawler_state["mode"] in ["PRIMARY", "FALLBACK"]:
        status = "healthy"
    else:
        status = "unhealthy"
    
    return {
        "status": status,
        "service": "crawl4ai-web-service-enhanced",
        "mode": crawler_state["mode"],
        "timestamp": int(time.time())
    }


if __name__ == "__main__":
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=5000,
        log_level="info"
    ) 