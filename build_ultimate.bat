@echo off
chcp 65001 >nul
echo 🚀 Crawl4AI Web Service Ultimate 打包脚本
echo ===============================================

REM 设置变量
set BUILD_NAME=crawl4ai-web-service-ultimate
set SPEC_FILE=%BUILD_NAME%.spec
set PYTHON_SCRIPT=web_service_ultimate.py

echo.
echo 📋 构建信息:
echo    目标文件: %PYTHON_SCRIPT%
echo    配置文件: %SPEC_FILE%
echo    输出名称: %BUILD_NAME%.exe
echo.

REM 检查Python环境
echo 🐍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查主要依赖
echo 📦 检查依赖包...
python -c "import crawl4ai; print('✅ Crawl4AI:', crawl4ai.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Crawl4AI未安装
    echo 正在安装...
    pip install crawl4ai
)

python -c "import playwright; print('✅ Playwright已安装')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Playwright未安装
    echo 正在安装...
    pip install playwright
    playwright install chromium
)

python -c "import fastapi; print('✅ FastAPI已安装')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ FastAPI未安装
    echo 正在安装...
    pip install fastapi uvicorn
)

python -c "import pyinstaller; print('✅ PyInstaller已安装')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PyInstaller未安装
    echo 正在安装...
    pip install pyinstaller
)

REM 检查必要文件
echo 📄 检查必要文件...
if not exist "%PYTHON_SCRIPT%" (
    echo ❌ 源文件不存在: %PYTHON_SCRIPT%
    pause
    exit /b 1
)

if not exist "%SPEC_FILE%" (
    echo ❌ 配置文件不存在: %SPEC_FILE%
    pause
    exit /b 1
)

echo ✅ 所有文件检查完成

REM 清理旧的构建文件
echo 🧹 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist\%BUILD_NAME%.exe" del /q "dist\%BUILD_NAME%.exe"
if exist "__pycache__" rmdir /s /q "__pycache__"

REM 检查Playwright浏览器
echo 🌐 检查Playwright浏览器...
python -c "from playwright.async_api import async_playwright; print('✅ Playwright可用')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ Playwright浏览器可能未安装，尝试安装...
    playwright install chromium
)

REM 开始构建
echo.
echo 🔨 开始构建 %BUILD_NAME%...
echo    这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --clean %SPEC_FILE%

REM 检查构建结果
if exist "dist\%BUILD_NAME%.exe" (
    echo.
    echo ✅ 构建成功！
    
    REM 获取文件大小
    for %%F in ("dist\%BUILD_NAME%.exe") do (
        set size=%%~zF
        set /a sizeMB=!size!/1024/1024
    )
    
    echo 📦 输出文件: dist\%BUILD_NAME%.exe
    echo 📏 文件大小: %sizeMB% MB
    
    REM 测试运行
    echo.
    echo 🧪 快速测试...
    echo 启动服务进行基本测试...
    
    start /b "测试服务" "dist\%BUILD_NAME%.exe"
    timeout /t 10 /nobreak >nul
    
    echo 测试服务状态...
    curl -s http://localhost:5000 >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 服务测试通过
    ) else (
        echo ⚠️ 服务测试可能需要更长时间启动
    )
    
    REM 停止测试服务
    taskkill /f /im "%BUILD_NAME%.exe" >nul 2>&1
    
    echo.
    echo 🎉 打包完成！
    echo 🔍 测试建议:
    echo    1. 运行: dist\%BUILD_NAME%.exe
    echo    2. 等待服务启动（约10-30秒）
    echo    3. 访问: http://localhost:5000
    echo    4. 测试API: http://localhost:5000/docs
    echo.
    echo 📋 功能特性:
    echo    ✅ Crawl4AI + Playwright引擎
    echo    ✅ 智能备用内容系统
    echo    ✅ 多策略反反爬虫
    echo    ✅ 自动降级机制
    echo    ✅ 支持复杂网站（如UNC）
    
) else (
    echo.
    echo ❌ 构建失败！
    echo 请检查上面的错误信息
    
    echo.
    echo 🔍 常见问题排查:
    echo    1. 检查Python环境和依赖包
    echo    2. 确保有足够的磁盘空间（需要约500MB）
    echo    3. 检查杀毒软件是否阻止了构建
    echo    4. 查看构建日志了解具体错误
)

echo.
echo 📂 构建文件位置:
if exist "dist" (
    dir /b dist\*.exe 2>nul
) else (
    echo 没有找到构建输出
)

echo.
pause 